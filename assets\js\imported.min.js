jQuery(document).ready((function(t){"use strict";let e=!1;t(".tablenav-pages").find(".current-page").on("focus",(()=>e=!0)).on("blur",(()=>e=!1)),t(".search-box").find('input[type="submit"]').on("click",(function(){let d=t(this).closest("form");e||d.find(".current-page").val(1)})),t(".vi-ui.tabular.menu .item").tab(),t(".vi-ui.accordion").accordion("refresh"),t("select.vi-ui.dropdown").dropdown(),t(".tmds-button-view-and-edit").on("click",(t=>t.stopPropagation())),t(".tmds-accordion-store-url").on("click",(t=>t.stopPropagation()));let d=[],o=!1,s=t(".tmds-imported-list-count");function n(){t(".tmds-delete-product-options-content-header-delete").addClass("tmds-hidden"),t(".tmds-delete-product-options-button-delete").addClass("tmds-hidden").data("product_id","").data("woo_product_id",""),t(".tmds-delete-product-options-delete-woo-product-wrap").addClass("tmds-hidden"),t(".tmds-delete-product-options").addClass("tmds-delete-product-options-editing"),t(".tmds-delete-product-options-container").addClass("tmds-hidden"),function(){let e=parseInt(t("html").css("top"));t("html").removeClass("tmds-noscroll"),window.scrollTo({top:-e,behavior:"instant"})}(),t(".tmds-delete-product-options-content-header-override").addClass("tmds-hidden"),t(".tmds-delete-product-options-button-override").addClass("tmds-hidden").data("product_id","").data("woo_product_id",""),t(".tmds-delete-product-options-override-product-wrap").addClass("tmds-hidden")}function a(e,n,i){let r=t(`.tmds-button-delete[data-product_id="${e}"]`),l=t(`#tmds-product-item-id-${e}`);l.find(".tmds-message").html(""),t.ajax({url:tmds_params.ajax_url,type:"POST",dataType:"JSON",data:{action:"tmds_delete_product",tmds_nonce:tmds_params.nonce,product_id:e,woo_product_id:n,delete_woo_product:i},success:function(e){if("success"===e.status){let e=parseInt(s.html());if(e>0){let t=parseInt(e-1);s.html(t),s.parent().attr("class","update-plugins count-"+t)}l.fadeOut(300),setTimeout((function(){l.remove(),function(){if(0===t(".tmds-accordion").length){let t=new URL(document.location.href);t.searchParams.delete("tmds_search_woo_id"),t.searchParams.delete("tmds_search_id"),t.searchParams.delete("tmds_search"),t.searchParams.delete("paged"),document.location.href=t.href}}()}),300)}else c(l,"negative",e.message),l.removeClass("tmds-accordion-deleting").accordion("open",0)},error:function(t){c(l,"negative",t.statusText),l.removeClass("tmds-accordion-deleting").accordion("open",0)},complete:function(){if(o=!1,r.removeClass("loading"),d.length>0){let t=d.shift();a(t.product_id,t.woo_product_id,t.delete_woo_product)}}})}function c(t,e,d){t.find(".tmds-message").html(`<div class="vi-ui message ${e}"><div>${d}</div></div>`)}t(".tmds-button-restore").on("click",(function(){let e=t(this),d=t(".tmds-imported-products-count-trash"),o=parseInt(d.html()),n=t(".tmds-imported-products-count-publish"),a=parseInt(n.html()),c={action:"tmds_restore_product",tmds_nonce:tmds_params.nonce,product_id:t(this).data("product_id")},i=t("#tmds-product-item-id-"+c.product_id);e.addClass("loading"),t.ajax({url:tmds_params.ajax_url,type:"post",dataType:"JSON",data:c,success:function(t){if("success"===t.status){let t=parseInt(s.html());if(t>0){let e=t+1;s.html(e),s.parent().attr("class","update-plugins count-"+e)}o--,a++,i.fadeOut(300),setTimeout((function(){d.html(o),n.html(a),i.remove()}),300)}t.message&&jQuery(document.body).trigger("villatheme_show_message",[t.message,[t.status],"",!1,4500])},error:function(t){console.log(t)},complete:function(){e.removeClass("loading")}})})),t(".tmds-button-trash").on("click",(function(){let e=t(this),d=t(".tmds-imported-products-count-trash"),o=parseInt(d.html()),n=t(".tmds-imported-products-count-publish"),a=parseInt(n.html()),c={action:"tmds_trash_product",tmds_nonce:tmds_params.nonce,product_id:t(this).data("product_id")},i=t("#tmds-product-item-id-"+c.product_id);e.addClass("loading"),t.ajax({url:tmds_params.ajax_url,type:"post",dataType:"JSON",data:c,success:function(t){if("success"===t.status){let t=parseInt(s.html());if(t>0){let e=t-1;s.html(e),s.parent().attr("class","update-plugins count-"+e)}o++,a--,i.fadeOut(300),setTimeout((function(){d.html(o),n.html(a),i.remove()}),300)}t.message&&jQuery(document.body).trigger("villatheme_show_message",[t.message,[t.status],"",!1,4500])},error:function(t){console.log(t)},complete:function(){e.removeClass("loading")}})})),t(".tmds-delete-product-options-button-delete").on("click",(function(){let e=t(this),s=e.data().product_id,c=e.data().woo_product_id;t(`.tmds-button-delete[data-product_id="${s}"]`).addClass("loading"),t(`#tmds-product-item-id-${s}`).addClass("tmds-accordion-deleting").accordion("close",0);let i=t(".tmds-delete-product-options-delete-woo-product").prop("checked")?1:0;n(),o?d.push({product_id:s,woo_product_id:c,delete_woo_product:i}):(o=!0,a(s,c,i))})),t(".tmds-button-delete").on("click",(function(){let e=t(this);if(!e.hasClass("loading")){let d=e.data().product_title,o=e.data().product_id,s=e.data().woo_product_id;t(".tmds-delete-product-options-product-title").html(d),t(".tmds-delete-product-options-button-delete").data("product_id",o).data("woo_product_id",s),t(".tmds-delete-product-options-content-header-delete").removeClass("tmds-hidden"),t(".tmds-delete-product-options-button-delete").removeClass("tmds-hidden"),t(".tmds-delete-product-options-delete-woo-product-wrap").removeClass("tmds-hidden"),t(".tmds-delete-product-options-container").removeClass("tmds-hidden"),function(){if(t(document).height()>t(window).height()){let e=t("html").scrollTop()?t("html").scrollTop():t("body").scrollTop();t("html").addClass("tmds-noscroll").css("top",-e)}}()}})),t(document).on("click",".tmds-overlay,.tmds-delete-product-options-close,.tmds-delete-product-options-button-cancel",(()=>n())),t(document).on("keydown",(function(e){t(".tmds-delete-product-options-container").hasClass("tmds-hidden")||(13==e.keyCode?t(".tmds-delete-product-options-button-override").hasClass("tmds-hidden")?t(".tmds-delete-product-options-button-delete").hasClass("tmds-hidden")||t(".tmds-delete-product-options-button-delete").trigger("click"):(t(".tmds-delete-product-options-button-override").trigger("click"),t(".tmds-delete-product-options-override-product").focus()):27===e.keyCode&&t(".tmds-overlay").trigger("click"))}))}));