# TMDS Dropshipping Plugin - Complete Technical Documentation

## Table of Contents
1. [System Architecture Overview](#system-architecture-overview)
2. [Core Plugin Files](#core-plugin-files)
3. [Admin Interface Files](#admin-interface-files)
4. [Core Logic Classes](#core-logic-classes)
5. [Background Processing System](#background-processing-system)
6. [Template Files](#template-files)
7. [External Integrations](#external-integrations)
8. [Data Flow Diagrams](#data-flow-diagrams)

## System Architecture Overview

The TMDS plugin implements a sophisticated multi-layered architecture that bridges Temu's product catalog with WooCommerce stores through a Chrome extension interface.

### High-Level Architecture

```mermaid
graph TB
    A[Chrome Extension] --> B[WordPress REST API]
    B --> C[Draft Products System]
    C --> D[Import Processing]
    D --> E[WooCommerce Products]
    
    F[Temu Website] --> A
    G[Background Processing] --> H[Image Downloads]
    G --> I[Currency Conversion]
    G --> J[Error Handling]
    
    K[Admin Interface] --> L[Settings Management]
    K --> M[Import List]
    K --> N[Product Management]
    
    subgraph "External Services"
        F
        O[Currency APIs]
        P[Image CDNs]
    end
    
    subgraph "WordPress Core"
        B
        Q[Custom Database Tables]
        R[WooCommerce Integration]
    end
```

## Core Plugin Files

### 1. `tmds-dropshipping-for-temu-and-woo.php` (261 lines)
**Purpose**: Main plugin initialization and bootstrap file

**Key Responsibilities**:
- Plugin header definition and metadata
- Environment validation (WordPress, WooCommerce, PHP versions)
- Class autoloading and dependency management
- Plugin activation/deactivation hooks
- Multisite compatibility checks

**Critical Code Sections**:
```php
// Plugin initialization
if (!class_exists('TMDS_DROPSHIPPING_FOR_TEMU_AND_WOO')) {
    class TMDS_DROPSHIPPING_FOR_TEMU_AND_WOO {
        protected static $instance = null;
        
        public function __construct() {
            $this->define_constants();
            $this->includes();
            $this->init_hooks();
        }
    }
}
```

**External Interactions**: None directly - serves as entry point

### 2. `includes/data.php` (892 lines)
**Purpose**: Core data management, settings engine, and utility functions

**Key Responsibilities**:
- Default settings configuration (870+ settings)
- Currency management (50+ supported currencies)
- Pricing rule definitions and calculations
- Database table creation and management
- Plugin compatibility checks

**Critical Features**:
- **Multi-tier pricing rules**: Fixed amount, percentage, multiply, set value
- **Currency conversion**: Real-time exchange rate support
- **Product configuration**: Status, visibility, categories, tags
- **Image management**: Download settings, external URL support
- **Variation handling**: Attribute mapping and variation creation

**External Interactions**:
- Currency exchange rate APIs
- Plugin compatibility checks (EXMAGE, VARGAL)

## Admin Interface Files

### 3. `admin/api.php` (418 lines)
**Purpose**: REST API endpoints for Chrome extension communication

**Key Endpoints**:
```php
// Product import endpoint
register_rest_route('tmds/v1', '/import-product', [
    'methods' => 'POST',
    'callback' => 'import_product_callback',
    'permission_callback' => 'check_permissions'
]);
```

**Security Features**:
- Nonce verification for all requests
- Capability checks for user permissions
- Data sanitization and validation
- Rate limiting and request throttling

**External Interactions**:
- Chrome extension authentication
- Temu product data reception
- Real-time import status updates

### 4. `admin/setup-wizard.php` (502 lines)
**Purpose**: Guided initial configuration and plugin recommendations

**Wizard Steps**:
1. Welcome and requirements check
2. Plugin recommendations (EXMAGE, VARGAL)
3. Basic settings configuration
4. Currency and pricing setup
5. Import preferences

**Plugin Integration**:
- Automatic plugin installation capabilities
- Compatibility validation
- Settings migration from similar plugins

### 5. `admin/import-list.php` (1,630 lines) - **LARGEST FILE**
**Purpose**: Core import functionality and product management interface

**Major Functions**:
- **Product import processing**: Handles single and bulk imports
- **Variation management**: Complex attribute mapping and variation creation
- **Image processing**: Gallery management and image assignment
- **Pricing calculations**: Real-time price rule application
- **Error handling**: Comprehensive error tracking and recovery

**Complex Workflows**:
```php
// Import processing workflow
public function import_products() {
    $products = $this->get_selected_products();
    foreach ($products as $product) {
        $this->validate_product_data($product);
        $this->process_variations($product);
        $this->handle_images($product);
        $this->apply_pricing_rules($product);
        $this->create_woocommerce_product($product);
    }
}
```

**External Interactions**:
- Temu product data processing
- Image downloads from external URLs
- WooCommerce product creation

### 6. `admin/imported.php` (842 lines)
**Purpose**: Imported products management and tracking

**Features**:
- Product status tracking and updates
- Bulk operations on imported products
- Search and filtering capabilities
- Synchronization with Temu updates
- Error resolution interface

### 7. `admin/settings.php` (704 lines)
**Purpose**: Comprehensive settings management interface

**Settings Categories**:
- General configuration
- Product import settings
- Pricing and currency rules
- Image management options
- Advanced features

**Field Rendering Engine**:
```php
// Dynamic field rendering
public function render_field($field_type, $field_config) {
    switch ($field_type) {
        case 'pricing_rules':
            return $this->render_pricing_rules_field($field_config);
        case 'currency_exchange':
            return $this->render_currency_field($field_config);
    }
}
```

### 8. `admin/error-images.php` (559 lines)
**Purpose**: Failed image management and recovery system

**Error Tracking**:
- Failed image download logging
- Retry mechanisms with exponential backoff
- Manual image replacement interface
- Bulk error resolution tools

## Core Logic Classes

### 9. `includes/class/tmds-products-table.php` (637 lines)
**Purpose**: Custom database operations for draft products

**Database Schema**:
```sql
CREATE TABLE wp_tmds_posts (
    ID bigint(20) NOT NULL AUTO_INCREMENT,
    post_title text,
    post_content longtext,
    post_status varchar(20),
    post_type varchar(20),
    PRIMARY KEY (ID)
);
```

**CRUD Operations**:
- Product creation and updates
- Batch operations for performance
- Custom query optimization
- Cache management integration

### 10. `includes/class/download-images.php` (175 lines)
**Purpose**: Background image processing and management

**Image Processing Workflow**:
```mermaid
graph LR
    A[Image URL] --> B[Validation]
    B --> C[Download Queue]
    C --> D[Background Processing]
    D --> E[WordPress Media Library]
    E --> F[Product Assignment]
    
    G[Error Handling] --> H[Retry Logic]
    H --> C
```

**Features**:
- Asynchronous image downloading
- Image optimization and resizing
- Error tracking and retry mechanisms
- CDN integration support

### 11. `includes/class/tmds-post.php` (347 lines)
**Purpose**: Custom post type handling with advanced caching

**Caching System**:
- WordPress object cache integration
- Custom cache invalidation
- Performance optimization for large datasets
- Memory management for bulk operations

## Background Processing System

### 12. `includes/background-process/wp-background-process.php` (501 lines)
**Purpose**: Sophisticated background processing engine

**Processing Architecture**:
```mermaid
graph TB
    A[Queue Item] --> B[Memory Check]
    B --> C[Time Limit Check]
    C --> D[Process Item]
    D --> E{Success?}
    E -->|Yes| F[Remove from Queue]
    E -->|No| G[Retry Logic]
    G --> H[Update Queue]
    F --> I{More Items?}
    H --> I
    I -->|Yes| B
    I -->|No| J[Complete Process]
```

**Key Features**:
- Memory limit management (90% threshold)
- Time limit enforcement (20 seconds default)
- Automatic retry mechanisms
- Cron-based health checks
- Process locking to prevent conflicts

### 13. `includes/background-process/wp-async-request.php` (167 lines)
**Purpose**: Asynchronous request handling for immediate responses

**Use Cases**:
- Image download initiation
- API calls to external services
- Non-blocking operations
- Real-time status updates

## External Integrations

### Chrome Extension Integration

```mermaid
sequenceDiagram
    participant CE as Chrome Extension
    participant TW as Temu Website
    participant WP as WordPress API
    participant DB as Database
    
    CE->>TW: Scrape Product Data
    TW-->>CE: Product Information
    CE->>WP: POST /api/import-product
    WP->>DB: Store Draft Product
    DB-->>WP: Confirmation
    WP-->>CE: Import Status
    CE->>CE: Update UI
```

### Temu Data Extraction

**Data Points Extracted**:
- Product title and description
- Pricing information
- Image galleries
- Variation attributes
- Shipping details
- Stock quantities

**Extraction Process**:
1. Chrome extension injects content scripts
2. DOM parsing for product information
3. Image URL collection and validation
4. Variation data extraction
5. Structured data transmission to WordPress

### Currency Exchange Integration

```mermaid
graph LR
    A[Pricing Rules] --> B[Currency Detection]
    B --> C[Exchange Rate API]
    C --> D[Rate Calculation]
    D --> E[Price Conversion]
    E --> F[WooCommerce Price]
    
    G[Cache Layer] --> C
    H[Fallback Rates] --> D
```

**Supported Currencies**: 50+ including USD, EUR, GBP, JPY, CNY, etc.
**Rate Sources**: Multiple API providers with fallback mechanisms
**Update Frequency**: Configurable (hourly, daily, weekly)

### Image Processing Pipeline

```mermaid
graph TB
    A[Temu Image URLs] --> B[URL Validation]
    B --> C[Download Queue]
    C --> D[Background Processor]
    D --> E[Image Download]
    E --> F[WordPress Upload]
    F --> G[Media Library]
    G --> H[Product Assignment]
    
    I[Error Handling] --> J[Retry Queue]
    J --> C
    
    K[CDN Support] --> E
    L[Image Optimization] --> F
```

## Data Flow Diagrams

### Complete Import Workflow

```mermaid
graph TB
    subgraph "Chrome Extension"
        A[User Clicks Import]
        B[Scrape Temu Data]
        C[Send to WordPress]
    end
    
    subgraph "WordPress API"
        D[Receive Product Data]
        E[Validate & Sanitize]
        F[Store in Draft Table]
    end
    
    subgraph "Import Processing"
        G[User Reviews Products]
        H[Configure Settings]
        I[Initiate Import]
        J[Background Processing]
    end
    
    subgraph "WooCommerce Integration"
        K[Create Product]
        L[Set Variations]
        M[Assign Images]
        N[Apply Pricing]
        O[Publish Product]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    L --> M
    M --> N
    N --> O
```

### Error Handling and Recovery

```mermaid
graph LR
    A[Operation Failure] --> B[Error Classification]
    B --> C{Error Type}
    C -->|Network| D[Retry with Backoff]
    C -->|Validation| E[User Notification]
    C -->|System| F[Log and Alert]
    
    D --> G[Success Check]
    G -->|Success| H[Continue Process]
    G -->|Failure| I[Max Retries?]
    I -->|No| D
    I -->|Yes| J[Move to Error Queue]
    
    E --> K[User Correction]
    K --> L[Reprocess]
    
    F --> M[Admin Notification]
    M --> N[Manual Resolution]
```

## Template Files Detailed Analysis

### 14. `templates/admin/html-import-list-item.php` (502 lines)
**Purpose**: Complex import list product UI with tabbed interface

**UI Components**:
- **Product Tab**: Basic product information, pricing, categories
- **Description Tab**: Rich text editor for product descriptions
- **Attributes Tab**: Dynamic attribute management with drag-and-drop
- **Variations Tab**: Complex variation table with filtering
- **Gallery Tab**: Image selection and management interface

**Interactive Features**:
```php
// Dynamic variation table loading
<table class="tmds-variations-table" data-product_id="<?php echo $product_id ?>">
    // AJAX-loaded variation rows with real-time pricing
</table>
```

**Real-time Calculations**:
- Currency conversion display
- Pricing rule application
- Stock quantity validation
- Image selection tracking

### 15. `templates/admin/html-price-rule-setting.php` (205 lines)
**Purpose**: Advanced pricing configuration interface

**Pricing Rule Types**:
1. **Fixed Amount**: Add/subtract fixed value
2. **Percentage**: Multiply by percentage
3. **Multiply**: Multiply by factor
4. **Set Value**: Override with specific price

**Rule Configuration**:
```php
// Multi-tier pricing structure
foreach ($pricing_ranges as $range) {
    // Quantity-based pricing rules
    // Category-specific overrides
    // Product-specific exceptions
}
```

### 16. `templates/admin/html-import-list-bulk-action-modal.php` (162 lines)
**Purpose**: Bulk operations modal for mass product management

**Bulk Operations**:
- Category assignment (add/replace)
- Tag management (add/replace)
- Shipping class assignment
- Status updates
- Pricing rule application

### 17. `templates/admin/html-import-list-header-section.php` (142 lines)
**Purpose**: Import list header with pagination and search

**Features**:
- Advanced search functionality
- Pagination controls
- Product count display
- Bulk action triggers
- Chrome extension integration buttons

### 18. `templates/admin/html-exchange-rates-setting.php` (63 lines)
**Purpose**: Currency exchange rate management interface

**Exchange Rate Features**:
- Manual rate override
- Automatic rate fetching
- Rate history tracking
- Multiple provider support

## Advanced Integration Patterns

### Chrome Extension Communication Protocol

```mermaid
sequenceDiagram
    participant U as User
    participant CE as Chrome Extension
    participant TW as Temu Website
    participant WP as WordPress
    participant BG as Background Process

    U->>CE: Click "Import Product"
    CE->>TW: Extract Product Data
    TW-->>CE: Product JSON
    CE->>CE: Validate Data Structure
    CE->>WP: POST /wp-json/tmds/v1/products

    Note over WP: Authentication & Validation
    WP->>WP: Create Draft Product
    WP-->>CE: {status: "success", product_id: 123}

    CE->>U: Show Success Message
    U->>WP: Navigate to Import List
    WP->>WP: Display Draft Products

    U->>WP: Configure & Import
    WP->>BG: Queue Background Tasks
    BG->>BG: Process Images
    BG->>BG: Apply Pricing Rules
    BG->>WP: Create WooCommerce Product
```

### Multi-Site Architecture Support

```mermaid
graph TB
    subgraph "Network Admin"
        A[Main Site]
        B[Site 1]
        C[Site 2]
        D[Site N]
    end

    subgraph "Shared Resources"
        E[Chrome Extension]
        F[Currency API]
        G[Image CDN]
    end

    subgraph "Site-Specific"
        H[Local Settings]
        I[Product Catalog]
        J[Customer Data]
    end

    E --> A
    E --> B
    E --> C
    E --> D

    F --> H
    G --> I

    A -.-> B
    A -.-> C
    A -.-> D
```

### Background Processing Deep Dive

```mermaid
graph TB
    subgraph "Queue Management"
        A[Add to Queue] --> B[Batch Creation]
        B --> C[Process Lock]
        C --> D[Item Processing]
    end

    subgraph "Resource Management"
        E[Memory Monitor] --> F{90% Limit?}
        G[Time Monitor] --> H{20s Limit?}
        F -->|Yes| I[Pause Processing]
        H -->|Yes| I
        I --> J[Schedule Next Batch]
    end

    subgraph "Error Handling"
        K[Task Failure] --> L[Retry Logic]
        L --> M{Max Retries?}
        M -->|No| N[Exponential Backoff]
        M -->|Yes| O[Move to Error Queue]
        N --> D
    end

    D --> E
    D --> G
    D --> K
```

## External Service Integrations

### Temu API Interaction Patterns

```mermaid
graph LR
    subgraph "Temu Website"
        A[Product Page]
        B[API Endpoints]
        C[Image Servers]
        D[Price Data]
    end

    subgraph "Chrome Extension"
        E[Content Script]
        F[Background Script]
        G[Data Parser]
    end

    subgraph "WordPress Plugin"
        H[REST API]
        I[Data Validator]
        J[Import Processor]
    end

    A --> E
    B --> F
    C --> G
    D --> G

    E --> H
    F --> I
    G --> J
```

### Currency Exchange Integration

```mermaid
sequenceDiagram
    participant P as Pricing Engine
    participant C as Cache Layer
    participant API1 as Primary Exchange API
    participant API2 as Fallback Exchange API
    participant DB as Database

    P->>C: Request USD to EUR rate
    C-->>P: Cache miss
    P->>API1: GET /latest?base=USD&symbols=EUR

    alt API1 Success
        API1-->>P: {"EUR": 0.85}
        P->>C: Cache rate (1 hour)
        P->>DB: Store rate history
    else API1 Failure
        P->>API2: GET /convert?from=USD&to=EUR
        API2-->>P: {"rate": 0.85}
        P->>C: Cache rate (30 min)
    end

    P->>P: Apply rate to product price
```

### Image Processing Pipeline

```mermaid
graph TB
    subgraph "Image Sources"
        A[Temu CDN]
        B[Product Images]
        C[Variation Images]
        D[Description Images]
    end

    subgraph "Processing Queue"
        E[Download Queue]
        F[Validation Queue]
        G[Optimization Queue]
        H[Assignment Queue]
    end

    subgraph "WordPress Integration"
        I[Media Library]
        J[Product Gallery]
        K[Variation Images]
        L[Featured Images]
    end

    A --> E
    B --> E
    C --> E
    D --> E

    E --> F
    F --> G
    G --> H

    H --> I
    I --> J
    I --> K
    I --> L

    subgraph "Error Handling"
        M[Failed Downloads]
        N[Retry Logic]
        O[Manual Resolution]
    end

    F -.-> M
    G -.-> M
    M --> N
    N --> O
```

## Remaining Core Files Analysis

### 19. `admin/recommend.php` (216 lines)
**Purpose**: Plugin recommendations and cross-selling system

**Recommendation Engine**:
- Compatible plugin detection
- Feature gap analysis
- Installation assistance
- Configuration migration

**Integration Points**:
```php
// Plugin compatibility matrix
$compatible_plugins = [
    'exmage' => ['min_version' => '1.0.0', 'features' => ['bulk_edit']],
    'vargal' => ['min_version' => '2.0.0', 'features' => ['variations']]
];
```

### 20. `admin/auth.php` (63 lines)
**Purpose**: Chrome extension authentication and authorization

**Authentication Flow**:
1. Chrome extension requests access
2. WordPress generates temporary credentials
3. OAuth-style token exchange
4. Secure API key generation
5. Permission scope validation

**Security Features**:
- Time-limited authentication tokens
- Scope-based permissions
- Request rate limiting
- Audit logging

### 21. `admin/log.php` (19 lines)
**Purpose**: WooCommerce logging integration

**Logging Categories**:
- Import operations
- Error tracking
- Performance metrics
- User actions
- API requests

### 22. `includes/class/error-images-table.php` (95 lines)
**Purpose**: Error tracking database for failed operations

**Error Classification**:
- Network timeouts
- Invalid image formats
- Permission errors
- Storage failures
- Processing errors

### 23. `includes/class/tmds-post-query.php` (23 lines)
**Purpose**: Custom query handling for draft products

**Query Optimizations**:
- Custom JOIN operations
- Metadata query optimization
- Performance indexing
- Cache integration

### 24. `includes/support.php` (1,047 lines)
**Purpose**: VillaTheme support framework integration

**Support Features**:
- Automated diagnostics
- System information collection
- Remote debugging capabilities
- Update management
- License validation

## Advanced System Patterns

### Database Architecture

```mermaid
erDiagram
    wp_tmds_posts {
        bigint ID PK
        text post_title
        longtext post_content
        varchar post_status
        varchar post_type
        datetime post_date
        bigint post_author
    }

    wp_tmds_postmeta {
        bigint meta_id PK
        bigint tmds_post_id FK
        varchar meta_key
        longtext meta_value
    }

    wp_tmds_error_images {
        bigint id PK
        bigint product_id FK
        varchar image_url
        varchar error_type
        datetime created_at
        int retry_count
    }

    wp_posts {
        bigint ID PK
        text post_title
        varchar post_type
        varchar post_status
    }

    wp_postmeta {
        bigint meta_id PK
        bigint post_id FK
        varchar meta_key
        longtext meta_value
    }

    wp_tmds_posts ||--o{ wp_tmds_postmeta : "has metadata"
    wp_tmds_posts ||--o{ wp_tmds_error_images : "has errors"
    wp_posts ||--o{ wp_postmeta : "has metadata"
    wp_tmds_posts ||--|| wp_posts : "becomes"
```

### State Management Flow

```mermaid
stateDiagram-v2
    [*] --> Scraped : Chrome Extension
    Scraped --> Draft : API Import
    Draft --> Configured : User Review
    Configured --> Processing : Import Trigger
    Processing --> Published : Success
    Processing --> Error : Failure
    Error --> Draft : Retry
    Error --> Failed : Max Retries
    Published --> Updated : Sync Changes
    Failed --> Manual : Admin Review
    Manual --> Draft : Correction

    state Processing {
        [*] --> ValidatingData
        ValidatingData --> ProcessingImages
        ProcessingImages --> ApplyingPricing
        ApplyingPricing --> CreatingProduct
        CreatingProduct --> [*]
    }
```

### Performance Optimization Patterns

```mermaid
graph TB
    subgraph "Caching Layers"
        A[Object Cache]
        B[Transient Cache]
        C[Database Query Cache]
        D[Image Cache]
    end

    subgraph "Optimization Techniques"
        E[Lazy Loading]
        F[Batch Processing]
        G[Background Tasks]
        H[CDN Integration]
    end

    subgraph "Monitoring"
        I[Performance Metrics]
        J[Error Tracking]
        K[Resource Usage]
        L[User Analytics]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L
```

## Security Architecture

### Authentication & Authorization

```mermaid
sequenceDiagram
    participant CE as Chrome Extension
    participant WP as WordPress
    participant DB as Database
    participant LOG as Audit Log

    CE->>WP: Request Authentication
    WP->>WP: Generate Nonce
    WP->>DB: Store Temp Credentials
    WP-->>CE: Auth Token

    CE->>WP: API Request + Token
    WP->>WP: Validate Token
    WP->>WP: Check Permissions
    WP->>LOG: Log Request

    alt Valid Request
        WP->>DB: Process Request
        DB-->>WP: Response Data
        WP-->>CE: Success Response
    else Invalid Request
        WP->>LOG: Log Security Event
        WP-->>CE: Error Response
    end
```

### Data Sanitization Pipeline

```mermaid
graph LR
    A[Raw Input] --> B[Type Validation]
    B --> C[Format Sanitization]
    C --> D[Content Filtering]
    D --> E[Security Scanning]
    E --> F[Database Preparation]

    G[XSS Protection] --> C
    H[SQL Injection Prevention] --> F
    I[CSRF Protection] --> B
    J[Rate Limiting] --> A
```

## Scalability Considerations

### Horizontal Scaling Pattern

```mermaid
graph TB
    subgraph "Load Balancer"
        A[Request Distribution]
    end

    subgraph "WordPress Instances"
        B[Instance 1]
        C[Instance 2]
        D[Instance N]
    end

    subgraph "Shared Services"
        E[Database Cluster]
        F[Redis Cache]
        G[File Storage]
        H[Background Queue]
    end

    A --> B
    A --> C
    A --> D

    B --> E
    B --> F
    B --> G
    B --> H

    C --> E
    C --> F
    C --> G
    C --> H

    D --> E
    D --> F
    D --> G
    D --> H
```

### Queue Management at Scale

```mermaid
graph TB
    subgraph "Queue Types"
        A[High Priority]
        B[Normal Priority]
        C[Low Priority]
        D[Retry Queue]
    end

    subgraph "Workers"
        E[Image Processor]
        F[Product Importer]
        G[Price Updater]
        H[Error Handler]
    end

    subgraph "Monitoring"
        I[Queue Length]
        J[Processing Time]
        K[Error Rate]
        L[Resource Usage]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L
```

## Integration Testing Patterns

### End-to-End Testing Flow

```mermaid
sequenceDiagram
    participant T as Test Suite
    participant CE as Chrome Extension Mock
    participant WP as WordPress
    participant DB as Test Database
    participant EXT as External APIs Mock

    T->>CE: Simulate Product Scraping
    CE->>WP: Send Product Data
    WP->>DB: Store Draft Product

    T->>WP: Trigger Import Process
    WP->>EXT: Request Currency Rates
    EXT-->>WP: Mock Exchange Rates
    WP->>EXT: Download Images
    EXT-->>WP: Mock Image Data

    WP->>DB: Create WooCommerce Product
    DB-->>WP: Product Created
    WP-->>T: Import Complete

    T->>T: Validate Product Data
    T->>T: Check Image Assignment
    T->>T: Verify Pricing Rules
```

This comprehensive technical documentation covers every aspect of the TMDS plugin architecture, from individual file responsibilities to complex system integrations and scalability patterns. The plugin represents a sophisticated enterprise-grade solution for dropshipping automation with robust error handling, security measures, and performance optimizations.
