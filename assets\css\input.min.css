/*!
 * # Semantic UI 2.2.12 - Input
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
.vi-ui.input{position:relative;font-weight:400;font-style:normal;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;color:rgba(0,0,0,.87);font-size:1em}.vi-ui.input input{margin:0;max-width:100%;-webkit-box-flex:1;-ms-flex:1 0 auto;flex:1 0 auto;outline:0;-webkit-tap-highlight-color:transparent;text-align:left;line-height:1.21428571em;font-family:Lato,'Helvetica Neue',Arial,Helvetica,sans-serif;padding:.67857143em 1em;background:#fff;border:1px solid rgba(34,36,38,.15);color:rgba(0,0,0,.87);border-radius:.28571429rem;-webkit-transition:box-shadow .1s ease,border-color .1s ease;transition:box-shadow .1s ease,border-color .1s ease;box-shadow:none}.vi-ui.input input::-webkit-input-placeholder{color:rgba(191,191,191,.87)}.vi-ui.input input::-moz-placeholder{color:rgba(191,191,191,.87)}.vi-ui.input input:-ms-input-placeholder{color:rgba(191,191,191,.87)}.vi-ui.disabled.input,.vi-ui.input:not(.disabled) input[disabled]{opacity:.45}.vi-ui.disabled.input input,.vi-ui.input:not(.disabled) input[disabled]{pointer-events:none}.vi-ui.input input:active,.vi-ui.input.down input{border-color:rgba(0,0,0,.3);background:#fafafa;color:rgba(0,0,0,.87);box-shadow:none}.vi-ui.loading.loading.input>i.icon:after,.vi-ui.loading.loading.input>i.icon:before{position:absolute;content:'';top:50%;left:50%;margin:-.64285714em 0 0 -.64285714em;width:1.28571429em;height:1.28571429em;border-radius:500rem}.vi-ui.loading.loading.input>i.icon:before{border:.2em solid rgba(0,0,0,.1)}.vi-ui.loading.loading.input>i.icon:after{-webkit-animation:button-spin .6s linear;animation:button-spin .6s linear;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;border-color:#767676 transparent transparent;border-style:solid;border-width:.2em;box-shadow:0 0 0 1px transparent}.vi-ui.input input:focus,.vi-ui.input.focus input{border-color:#85b7d9;background:#fff;color:rgba(0,0,0,.8);box-shadow:none}.vi-ui.input input:focus::-webkit-input-placeholder,.vi-ui.input.focus input::-webkit-input-placeholder{color:rgba(115,115,115,.87)}.vi-ui.input input:focus::-moz-placeholder,.vi-ui.input.focus input::-moz-placeholder{color:rgba(115,115,115,.87)}.vi-ui.input input:focus:-ms-input-placeholder,.vi-ui.input.focus input:-ms-input-placeholder{color:rgba(115,115,115,.87)}.vi-ui.input.error input{background-color:#fff6f6;border-color:#e0b4b4;color:#9f3a38;box-shadow:none}.vi-ui.input.error input::-webkit-input-placeholder{color:#e7bdbc}.vi-ui.input.error input::-moz-placeholder{color:#e7bdbc}.vi-ui.input.error input:-ms-input-placeholder{color:#e7bdbc!important}.vi-ui.input.error input:focus::-webkit-input-placeholder{color:#da9796}.vi-ui.input.error input:focus::-moz-placeholder{color:#da9796}.vi-ui.input.error input:focus:-ms-input-placeholder{color:#da9796!important}.vi-ui.transparent.input input{border-color:transparent!important;background-color:transparent!important;padding:0!important;box-shadow:none!important;border-radius:0!important}.vi-ui.transparent.icon.input>i.icon{width:1.1em}.vi-ui.transparent.icon.input>input{padding-left:0!important;padding-right:2em!important}.vi-ui.transparent[class*="left icon"].input>input{padding-left:2em!important;padding-right:0!important}.vi-ui.transparent.inverted.input{color:#fff}.vi-ui.transparent.inverted.input input{color:inherit}.vi-ui.transparent.inverted.input input::-webkit-input-placeholder{color:rgba(255,255,255,.5)}.vi-ui.transparent.inverted.input input::-moz-placeholder{color:rgba(255,255,255,.5)}.vi-ui.transparent.inverted.input input:-ms-input-placeholder{color:rgba(255,255,255,.5)}.vi-ui.icon.input>i.icon{cursor:default;position:absolute;line-height:1;text-align:center;top:0;right:0;margin:0;height:100%;width:2.67142857em;opacity:.5;border-radius:0 .28571429rem .28571429rem 0;-webkit-transition:opacity .3s ease;transition:opacity .3s ease}.vi-ui.icon.input>i.icon:not(.link){pointer-events:none}.vi-ui.icon.input input{padding-right:2.67142857em!important}.vi-ui.icon.input>i.icon:after,.vi-ui.icon.input>i.icon:before{left:0;position:absolute;text-align:center;top:50%;width:100%;margin-top:-.5em}.vi-ui.icon.input>i.link.icon{cursor:pointer}.vi-ui.icon.input>i.circular.icon{top:.35em;right:.5em}.vi-ui[class*="left icon"].input>i.icon{right:auto;left:1px;border-radius:.28571429rem 0 0 .28571429rem}.vi-ui[class*="left icon"].input>i.circular.icon{right:auto;left:.5em}.vi-ui[class*="left icon"].input>input{padding-left:2.67142857em!important;padding-right:1em!important}.vi-ui.icon.input>input:focus~i.icon{opacity:1}.vi-ui.labeled.input>.label{-webkit-box-flex:0;-ms-flex:0 0 auto;flex:0 0 auto;margin:0;font-size:1em}.vi-ui.labeled.input>.label:not(.corner){padding-top:.78571429em;padding-bottom:.78571429em}.vi-ui.labeled.input:not([class*="corner labeled"]) .label:first-child{border-top-right-radius:0;border-bottom-right-radius:0}.vi-ui.labeled.input:not([class*="corner labeled"]) .label:first-child+input{border-top-left-radius:0;border-bottom-left-radius:0;border-left-color:transparent}.vi-ui.labeled.input:not([class*="corner labeled"]) .label:first-child+input:focus{border-left-color:#85b7d9}.vi-ui[class*="right labeled"].input input{border-top-right-radius:0!important;border-bottom-right-radius:0!important;border-right-color:transparent!important}.vi-ui[class*="right labeled"].input input+.label{border-top-left-radius:0;border-bottom-left-radius:0}.vi-ui[class*="right labeled"].input input:focus{border-right-color:#85b7d9!important}.vi-ui.labeled.input .corner.label{top:1px;right:1px;font-size:.64285714em;border-radius:0 .28571429rem 0 0}.vi-ui[class*="corner labeled"]:not([class*="left corner labeled"]).labeled.input input{padding-right:2.5em!important}.vi-ui[class*="corner labeled"].icon.input:not([class*="left corner labeled"])>input{padding-right:3.25em!important}.vi-ui[class*="corner labeled"].icon.input:not([class*="left corner labeled"])>.icon{margin-right:1.25em}.vi-ui[class*="left corner labeled"].labeled.input input{padding-left:2.5em!important}.vi-ui[class*="left corner labeled"].icon.input>input{padding-left:3.25em!important}.vi-ui[class*="left corner labeled"].icon.input>.icon{margin-left:1.25em}.vi-ui.input>.vi-ui.corner.label{top:1px;right:1px}.vi-ui.input>.vi-ui.left.corner.label{right:auto;left:1px}.vi-ui.action.input>.button,.vi-ui.action.input>.buttons{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-flex:0;-ms-flex:0 0 auto;flex:0 0 auto}.vi-ui.action.input>.button,.vi-ui.action.input>.buttons>.button{padding-top:.78571429em;padding-bottom:.78571429em;margin:0}.vi-ui.action.input:not([class*="left action"])>input{border-top-right-radius:0!important;border-bottom-right-radius:0!important;border-right-color:transparent!important}.vi-ui.action.input:not([class*="left action"])>.button:not(:first-child),.vi-ui.action.input:not([class*="left action"])>.buttons:not(:first-child)>.button,.vi-ui.action.input:not([class*="left action"])>.dropdown:not(:first-child){border-radius:0}.vi-ui.action.input:not([class*="left action"])>.button:last-child,.vi-ui.action.input:not([class*="left action"])>.buttons:last-child>.button,.vi-ui.action.input:not([class*="left action"])>.dropdown:last-child{border-radius:0 .28571429rem .28571429rem 0}.vi-ui.action.input:not([class*="left action"]) input:focus{border-right-color:#85b7d9!important}.vi-ui[class*="left action"].input>input{border-top-left-radius:0!important;border-bottom-left-radius:0!important;border-left-color:transparent!important}.vi-ui[class*="left action"].input>.button,.vi-ui[class*="left action"].input>.buttons>.button,.vi-ui[class*="left action"].input>.dropdown{border-radius:0}.vi-ui[class*="left action"].input>.button:first-child,.vi-ui[class*="left action"].input>.buttons:first-child>.button,.vi-ui[class*="left action"].input>.dropdown:first-child{border-radius:.28571429rem 0 0 .28571429rem}.vi-ui[class*="left action"].input>input:focus{border-left-color:#85b7d9!important}.vi-ui.inverted.input input{border:0}.vi-ui.fluid.input{display:-webkit-box;display:-ms-flexbox;display:flex}.vi-ui.fluid.input>input{width:0!important}.vi-ui.mini.input{font-size:.78571429em}.vi-ui.small.input{font-size:.92857143em}.vi-ui.large.input{font-size:1.14285714em}.vi-ui.big.input{font-size:1.28571429em}.vi-ui.huge.input{font-size:1.42857143em}.vi-ui.massive.input{font-size:1.71428571em}