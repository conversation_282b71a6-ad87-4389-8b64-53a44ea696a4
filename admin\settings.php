<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class TMDS_Admin_Settings {
	protected static $settings;

	public function __construct() {
		self::$settings = TMDS_DATA::get_instance();
		add_action( 'admin_menu', array( $this, 'admin_menu' ) );
		add_filter( 'set-screen-option', array( $this, 'save_screen_options' ), 10, 3 );
		add_action( 'admin_init', array( $this, 'save_settings' ), 99 );
		add_action( 'admin_enqueue_scripts', array( $this, 'admin_enqueue_scripts' ) );
		$this->add_ajax_events();
		add_filter( 'wp_admin_css_uri', array( $this, 'connect_extension' ), 10, 2 );
	}
    public function connect_extension($result, $file){
        if ($file === 'tmds-connect-extension'){
            $result = WC()->plugin_url()  . '/assets/css/auth.css' ;
        }
        return $result;
    }
	public function admin_menu() {
		$menu_slug = self::$settings::$prefix;
		add_menu_page(
			TMDS_NAME,
			esc_html__( 'TMDS', 'tmds-dropshipping-for-temu-and-woo' ),
			apply_filters( "villatheme_{$menu_slug}_admin_menu_capability", 'manage_options', $menu_slug ),
			$menu_slug,
			array( TMDS_Admin_Class_Prefix . 'Import_List', 'page_callback' ),
			TMDS_IMAGES . 'icon.png',
			2
		);
		$import_list   = add_submenu_page(
			$menu_slug,
			esc_html__( 'Import List', 'tmds-dropshipping-for-temu-and-woo' ),
			esc_html__( 'Import List', 'tmds-dropshipping-for-temu-and-woo' ),
			apply_filters( "villatheme_{$menu_slug}_admin_sub_menu_capability", 'manage_woocommerce', "{$menu_slug}-import-list" ),
			$menu_slug,
			array( TMDS_Admin_Class_Prefix . 'Import_List', 'page_callback' )
		);
		$imported_list = add_submenu_page( $menu_slug,
			sprintf( esc_html__( 'Imported Products - %s', 'tmds-dropshipping-for-temu-and-woo' ), TMDS_NAME ),// phpcs:ignore WordPress.WP.I18n.MissingTranslatorsComment
			esc_html__( 'Imported', 'tmds-dropshipping-for-temu-and-woo' ),
			apply_filters( "villatheme_{$menu_slug}_admin_sub_menu_capability", 'manage_woocommerce', "{$menu_slug}-imported" ),
			"{$menu_slug}-imported",
			array( TMDS_Admin_Class_Prefix . 'Imported', 'page_callback' )
		);
		$failed_image  = add_submenu_page( $menu_slug,
			esc_html__( 'Failed Images', 'tmds-dropshipping-for-temu-and-woo' ),
			esc_html__( 'Failed Images', 'tmds-dropshipping-for-temu-and-woo' ),
			apply_filters( "villatheme_{$menu_slug}_admin_sub_menu_capability", 'manage_woocommerce', "{$menu_slug}-error-images" ),
			"{$menu_slug}-error-images",
			array( TMDS_Admin_Class_Prefix . 'Error_Images', 'page_callback' )
		);
		add_submenu_page(
			$menu_slug,
			esc_html__( 'Settings', 'tmds-dropshipping-for-temu-and-woo' ),
			esc_html__( 'Settings', 'tmds-dropshipping-for-temu-and-woo' ),
			apply_filters( "villatheme_{$menu_slug}_admin_sub_menu_capability", 'manage_woocommerce', "{$menu_slug}-settings" ),
			"{$menu_slug}-settings",
			array( $this, 'page_callback' )
		);
		add_action( "load-$import_list", array( TMDS_Admin_Class_Prefix . 'Import_List', 'screen_options_page' ) );
		add_action( "load-$imported_list", array( TMDS_Admin_Class_Prefix . 'Imported', 'screen_options_page' ) );
		add_action( "load-$failed_image", array( TMDS_Admin_Class_Prefix . 'Error_Images', 'screen_options_page' ) );
	}

	public function save_screen_options( $result, $option, $value ) {
		if ( in_array( $option,
			[
				self::$settings::$prefix . '_import_list_per_page',
				self::$settings::$prefix . '_imported_per_page',
				self::$settings::$prefix . '_error_images_per_page'
			] )
		) {
			return $value;
		}

		return $result;
	}

	public function save_settings() {
		global $tmds_params;
		if ( ! current_user_can( apply_filters( "villatheme_tmds_admin_sub_menu_capability", 'manage_woocommerce', "tmds-settings" ) ) ) {
			return;
		}
		if ( ! isset( $_POST["_tmds_settings_nonce"] ) || ! wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST["_tmds_settings_nonce"] ) ), "tmds_settings" ) ) {
			return;
		}
		if ( isset( $_POST["tmds_wizard_submit"] ) ) {
			/*Save settings for setup wizard*/
			$args = self::$settings->get_default();
			foreach ( $args as $key => $arg ) {
				if ( isset( $_POST[ $key ] ) ) {
					if ( is_array( $_POST[ $key ] ) ) {
						$args[ $key ] = array_map( 'sanitize_text_field', wp_unslash( $_POST[ $key ] ) );
					} else {
						$args[ $key ] = sanitize_text_field( wp_unslash( $_POST[ $key ] ) );
					}
				}
			}
		} else {
			$args = self::$settings->get_params();
			foreach ( $args as $key => $arg ) {
				if ( isset( $_POST[ $key ] ) ) {
					if ( is_array( $_POST[ $key ] ) ) {
						$args[ $key ] = array_map( 'sanitize_text_field',wp_unslash( $_POST[ $key ] ) );
					} else {
						$args[ $key ] = sanitize_text_field( wp_unslash( $_POST[ $key ] ) );
					}
				} else {
					$args[ $key ] = is_array( $arg ) ? array() : '';
				}
			}
		}
		$args                    = apply_filters( "tmds_save_plugin_settings_params", $args );
		$tmds_params = $args;
		update_option( 'tmds_params', $args );
		if ( isset( $_POST["tmds_setup_redirect"] ) ) {
			wp_safe_redirect( esc_url_raw( wp_unslash( $_POST["tmds_setup_redirect"] ) ) );
			exit;
		}
		self::$settings = TMDS_DATA::get_instance( true );
	}

	public function page_callback() {
		$tabs       = array(
			'general' => esc_html__( 'General', 'tmds-dropshipping-for-temu-and-woo' ),
			'product' => esc_html__( 'Product', 'tmds-dropshipping-for-temu-and-woo' ),
			'price'   => esc_html__( 'Product price', 'tmds-dropshipping-for-temu-and-woo' )
		);
		$tab_active = array_key_first( $tabs );
		?>
        <div class="wrap">
            <h2><?php printf( esc_html__( '%s Settings', 'tmds-dropshipping-for-temu-and-woo' ), wp_kses_post( TMDS_NAME ) );// phpcs:ignore WordPress.WP.I18n.MissingTranslatorsComment
				?></h2>
			<?php
			$messages = array();
			if ( self::$settings::get_disable_wp_cron() ) {
				$messages[]
					= wp_kses_post( __( '<strong>DISABLE_WP_CRON</strong> is set to true, product images may not be downloaded properly. Please try option <strong>"Disable background process"</strong>',
					'tmds-dropshipping-for-temu-and-woo' ) );
			}
			if ( ! empty( $messages ) ) {
				?>
                <div class="vi-ui message negative">
                    <div class="header"><?php esc_html_e( 'TMDS - Warning', 'tmds-dropshipping-for-temu-and-woo' ) ?>
                        :
                    </div>
                    <ul class="list">
						<?php
						foreach ( $messages as $message ) {
							?>
                            <li><?php echo wp_kses( $message,self::$settings::filter_allowed_html() );  ?></li>
							<?php
						}
						?>
                    </ul>
                </div>
				<?php
			}
			?>
            <form method="post" class="vi-ui small form">
				<?php wp_nonce_field( 'tmds_settings', '_tmds_settings_nonce' ); ?>
                <div class="vi-ui attached tabular menu">
					<?php
					foreach ( $tabs as $slug => $text ) {
						$active = $tab_active === $slug ? 'active' : '';
						printf( ' <a class="item %s" data-tab="%s">%s</a>', esc_attr( $active ), esc_attr( $slug ), esc_html( $text ) );
					}
					?>
                </div>
				<?php
				foreach ( $tabs as $slug => $text ) {
					$active = $tab_active === $slug ? 'active' : '';
					$method = str_replace( '-', '_', $slug ) . '_options';
					$fields = [];
					printf( '<div class="vi-ui bottom attached %s tab segment" data-tab="%s">', esc_attr( $active ), esc_attr( $slug ) );
					if ( method_exists( $this, $method ) ) {
						$fields = $this->$method();
					}
					self::$settings::villatheme_render_table_field( apply_filters( "tmds_settings_fields", $fields, $slug ) );
					do_action( 'tmds_settings_tab', $slug );
					printf( '</div>' );
				}
				?>
                <p class="tmds-save-settings-container">
                    <button type="submit" class="vi-ui button labeled icon primary tmds-save-settings"
                            name="tmds_save_settings">
                        <i class="save icon"> </i>
						<?php esc_html_e( 'Save Settings', 'tmds-dropshipping-for-temu-and-woo' ); ?>
                    </button>
					<?php
					self::$settings::chrome_extension_buttons();
					?>
                </p>
            </form>
			<?php do_action( 'villatheme_support_tmds-dropshipping-for-temu-and-woo' ) ?>
        </div>
		<?php
	}

	public static function price_options() {
		?>
        <div class="vi-ui styled fluid accordion">
            <div class="title active">
                <i class="dropdown icon"> </i>
				<?php esc_html_e( 'Exchange rates', 'tmds-dropshipping-for-temu-and-woo' ); ?>
            </div>
            <div class="content active">
                <div class="vi-ui positive small message">
                    <p><?php esc_html_e( 'These are the exchange rates to convert from currency on Temu to your store currency when adding products to the import list.', 'tmds-dropshipping-for-temu-and-woo' ) ?></p>
                    <p><?php esc_html_e( 'E.g: Your Woocommerce store currency is VND, exchange rate for USD is: 1 USD = 23 000 VND',
							'tmds-dropshipping-for-temu-and-woo' ) ?></p>
                    <p><?php esc_html_e( '=> set "Exchange rate" 23 000 for row "USD"', 'tmds-dropshipping-for-temu-and-woo' ) ?></p>
                </div>
				<?php
				wc_get_template( 'admin/html-exchange-rates-setting.php',
					array( 'settings' => self::$settings ),
					'',
					TMDS_TEMPLATES );
				?>
            </div>
        </div>
        <div></div>
        <div class="vi-ui styled fluid accordion tmds-price-rules-wrap">
            <div class="title active">
                <i class="dropdown icon"> </i>
				<?php esc_html_e( 'Price rules', 'tmds-dropshipping-for-temu-and-woo' ); ?>
            </div>
            <div class="content active">
                <div class="vi-ui positive small message">
					<?php esc_html_e( 'For each price, first matched rule(from top to bottom) will be applied. If no rules match, the default will be used.',
						'tmds-dropshipping-for-temu-and-woo' ) ?>
                </div>
				<?php
				wc_get_template( 'admin/html-price-rule-setting.php',
					array( 'settings' => self::$settings ),
					'',
					TMDS_TEMPLATES );
				?>
            </div>
        </div>
		<?php
		return [];
	}

	public static function product_options() {
		$fields = [
			'product_status'              => [
				'type'    => 'select',
				'options' => self::$settings::get_product_status_options(),
				'value'   => self::$settings->get_params( 'product_status' ),
				'title'   => esc_html__( 'Product status', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'use_global_attributes'       => [
				'type'  => 'checkbox',
				'value' => self::$settings->get_params( 'use_global_attributes' ),
				'title' => esc_html__( 'Use global attributes', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'  => wp_kses_post( __( 'Global attributes will be used instead of custom attributes. More details about <a href="https://woocommerce.com/document/managing-product-taxonomies/#product-attributes" target="_blank">Product attributes</a>',
					'tmds-dropshipping-for-temu-and-woo' ) ),
			],
			'simple_if_one_variation'     => [
				'type'  => 'checkbox',
				'value' => self::$settings->get_params( 'simple_if_one_variation' ),
				'title' => esc_html__( 'Import as simple product', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'  => esc_html__( 'If a product has only 1 variation or you select only 1 variation to import, that product will be imported as simple product. Variation sku and attributes will not be used.',
					'tmds-dropshipping-for-temu-and-woo' ),
			],
			'catalog_visibility'          => [
				'type'    => 'select',
				'options' => self::$settings::get_catalog_visibility_options(),
				'value'   => self::$settings->get_params( 'catalog_visibility' ),
				'title'   => esc_html__( 'Catalog visibility', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'    => esc_html__( 'This setting determines which shop pages products will be listed on.', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'product_description'         => [
				'type'    => 'select',
				'options' => array(
					'none'                           => esc_html__( 'None', 'tmds-dropshipping-for-temu-and-woo' ),
					'item_specifics'                 => esc_html__( 'Item specifics', 'tmds-dropshipping-for-temu-and-woo' ),
					'description'                    => esc_html__( 'Product Description', 'tmds-dropshipping-for-temu-and-woo' ),
					'item_specifics_and_description' => esc_html__( 'Item specifics &amp; Product Description', 'tmds-dropshipping-for-temu-and-woo' ),
				),
				'value'   => self::$settings->get_params( 'product_description' ),
				'title'   => esc_html__( 'Product description', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'    => esc_html__( 'Default product description in the import list.', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'use_external_image'          => [
				'type'  => 'checkbox',
				'value' => self::$settings->get_params( 'use_external_image' ),
				'title' => esc_html__( 'Use external links for images', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'  => esc_html__( 'This helps save storage by using original Temu image URLs but you will not be able to edit them', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'download_description_images' => [
				'type'  => 'checkbox',
				'value' => self::$settings->get_params( 'download_description_images' ),
				'wrap_class' => 'tmds-use_external_image-disable-class',
				'title' => esc_html__( 'Import description images', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'  => esc_html__( 'Upload images in product description if any. If disabled, images in description will use the original Temu cdn links', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'product_gallery'             => [
				'type'  => 'checkbox',
				'value' => self::$settings->get_params( 'product_gallery' ),
				'title' => esc_html__( 'Default select product images', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'  => esc_html__( 'First image will be selected as product image and other images(except images from product description) are selected in gallery when adding product to import list', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'disable_background_process'  => [
				'type'  => 'checkbox',
				'value' => self::$settings->get_params( 'disable_background_process' ),
				'title' => esc_html__( 'Disable background process', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'  => esc_html__( 'When importing products, instead of letting their images import in the background, main product image will be uploaded immediately while gallery and variation images(if any) will be added to Failed images page so that you can go there to import them manually.', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'product_categories'          => [
				'type'              => 'select2',
				'multiple'          => 1,
				'custom_attributes' => [
					'data-type_select2' => 'category'
				],
				'value'             => self::$settings->get_params( 'product_categories' ),
				'title'             => esc_html__( 'Default categories', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'              => esc_html__( 'Imported products will be added to these categories.', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'product_tags'                => [
				'type'              => 'select2',
				'multiple'          => 1,
				'custom_attributes' => [
					'data-type_select2' => 'tag'
				],
				'value'             => self::$settings->get_params( 'product_tags' ),
				'title'             => esc_html__( 'Default product tags', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'              => esc_html__( 'Imported products will be added these tags.', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'product_shipping_class'      => [
				'type'    => 'select',
				'options' => [ '0' => esc_html__( 'No shipping class', 'tmds-dropshipping-for-temu-and-woo' ) ] + self::$settings::get_shipping_class_options(),
				'value'   => self::$settings->get_params( 'product_shipping_class' ),
				'title'   => esc_html__( 'Default shipping class', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'    => esc_html__( 'Shipping class selected here will also be selected by default in the Import list', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'variation_visible'           => [
				'type'  => 'checkbox',
				'value' => self::$settings->get_params( 'variation_visible' ),
				'title' => esc_html__( 'Product variations is visible on product page', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'  => esc_html__( 'Enable to make variations of imported products visible on product page', 'tmds-dropshipping-for-temu-and-woo' ),
			],
			'manage_stock'                => [
				'type'  => 'checkbox',
				'value' => self::$settings->get_params( 'manage_stock' ),
				'title' => esc_html__( 'Manage stock', 'tmds-dropshipping-for-temu-and-woo' ),
				'desc'  => esc_html__( 'Enable manage stock and import product inventory. If this option is disabled, products stock status will be set "Instock" and product inventory will not be imported', 'tmds-dropshipping-for-temu-and-woo' ),
			],
		];
		if ( ! class_exists( 'EXMAGE_WP_IMAGE_LINKS' ) ) {
			$plugins     = get_plugins();
			$plugin_slug = 'exmage-wp-image-links';
			$plugin      = "{$plugin_slug}/{$plugin_slug}.php";
			if ( ! isset( $plugins[ $plugin ] ) ) {
				$tmp = sprintf( '<a href="%s" target="_blank" class="button button-primary">%s</a>',
					esc_url( wp_nonce_url( self_admin_url( "update.php?action=install-plugin&plugin={$plugin_slug}" ), "install-plugin_{$plugin_slug}" ) ),
					esc_html__( 'Install now', 'tmds-dropshipping-for-temu-and-woo' ) );
			} else {
				$tmp = sprintf( '<a href="%s" target="_blank" class="button button-primary">%s</a>', esc_url( wp_nonce_url( add_query_arg( array(
					'action' => 'activate',
					'plugin' => $plugin
				), admin_url( 'plugins.php' ) ), "activate-plugin_{$plugin}" ) ), esc_html__( 'Activate now', 'tmds-dropshipping-for-temu-and-woo' ) );
			}
			ob_start();
			echo wp_kses( '<p class="description"><strong>*</strong>' ,TMDS_DATA::filter_allowed_html());
			echo wp_kses(sprintf( esc_html__( 'To use this feature, you have to install and activate %s plugin. %s', 'tmds-dropshipping-for-temu-and-woo' ),// phpcs:ignore WordPress.WP.I18n.MissingTranslatorsComment, WordPress.WP.I18n.UnorderedPlaceholdersText
				'<a target="_blank" href="https://wordpress.org/plugins/exmage-wp-image-links/">EXMAGE – WordPress Image Links</a>', $tmp ),TMDS_DATA::filter_allowed_html() );
			printf( '</p>' );
			$after_desc                   = ob_get_clean();
			$fields['use_external_image'] += [
				'disabled'   => 1,
				'after_desc' => $after_desc,
			];
		}elseif (!empty($fields['use_external_image']['value'])){
            $fields['download_description_images']['wrap_class']='tmds-use_external_image-disable-class tmds-hidden';
		}

		return [
			'section_start' => [],
			'section_end'   => [],
			'fields'        => $fields
		];
	}

	public static function general_options() {
		return [
			'section_start' => [],
			'section_end'   => [],
			'fields'        => [
				'enable'                    => [
					'type'  => 'checkbox',
					'value' => self::$settings->get_params( 'enable' ),
					'title' => esc_html__( 'Enable', 'tmds-dropshipping-for-temu-and-woo' ),
					/* translators: TMDS plugin name */
					'desc'  => sprintf(esc_html__( 'You need to enable this to let %s connect to your store', 'tmds-dropshipping-for-temu-and-woo' ),TMDS_NAME),
				],
				'install_and_use_extension' => [
					'type'  => 'html',
					'html'  => sprintf( '<p><a href="https://downloads.villatheme.com/?download=tmds-extension" target="_blank">%s</a></p>
                    <p class="description"><strong>*</strong>%s</p>
                    <div class="vi-ui fluid styled accordion tmds-video-guide-wrap">
                        <div class="title active">
                            <i class="dropdown icon"></i>
							%s
                        </div>
                        <div class="content active">
                            <iframe width="560" height="315" src="https://www.youtube.com/embed/1HazQ0zspns?si=6IhxQla7STTxFoK6" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                        </div>
                    </div>',
						/* translators: TMDS plugin name */
						sprintf(esc_html__( 'Add %s Extension', 'tmds-dropshipping-for-temu-and-woo' ),TMDS_NAME),
						esc_html__( 'To import Temu products, this chrome extension is required.', 'tmds-dropshipping-for-temu-and-woo' ),
						esc_html__( 'Install and use', 'tmds-dropshipping-for-temu-and-woo' ) ),
					'title' => esc_html__( 'Video guide', 'tmds-dropshipping-for-temu-and-woo' ),
				],
			]
		];
	}

	public function admin_enqueue_scripts() {
		$menu_slug = self::$settings::$prefix;
		$enqueue   = false;
		$page      = isset( $_GET['page'] ) ? sanitize_text_field( wp_unslash( $_GET['page'] ) ) : '';
		if ( in_array( $page, [
			$menu_slug,
			$menu_slug . '-import-list',
			$menu_slug . '-imported',
			$menu_slug . '-settings',
			$menu_slug . '-error-images'
		] ) ) {
			$enqueue = true;
		} elseif ( isset( $_GET['tmds_setup_wizard'], $_GET['_wpnonce'] ) && ! empty( $_GET['tmds_setup_wizard'] )
		           && wp_verify_nonce( sanitize_text_field( wp_unslash( $_GET['_wpnonce'] ) ), 'tmds_setup' )
		) {
			$enqueue = true;
		}
		if ( ! $enqueue ) {
			return;
		}
		self::$settings::enqueue_style(
			array(
				'semantic-ui-button',
				'semantic-ui-checkbox',
				'semantic-ui-dropdown',
				'semantic-ui-segment',
				'semantic-ui-form',
				'semantic-ui-label',
				'semantic-ui-input',
				'semantic-ui-icon',
				'semantic-ui-table',
				'transition',
				'select2'
			),
			array(
				'button',
				'checkbox',
				'dropdown',
				'segment',
				'form',
				'label',
				'input',
				'icon',
				'table',
				'transition',
				'select2'
			),
			array( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 )
		);
		self::$settings::enqueue_style(
			array( $menu_slug . '-admin-settings', 'villatheme-show-message' ),
			array( 'admin-settings', 'villatheme-show-message' ),
			array( 0, 0 )
		);
		self::$settings::enqueue_script(
			array( 'semantic-ui-address', 'semantic-ui-checkbox', 'semantic-ui-dropdown', 'transition', 'select2' ),
			array( 'address', 'checkbox', 'dropdown', 'transition', 'select2' ),
			array( 1, 1, 1, 1, 1 )
		);
		self::$settings::enqueue_script(
			array( 'villatheme-show-message' ),
			array( 'villatheme-show-message' ),
			array( 0 ),
			array( array( 'jquery' ) )
		);
		$params          = array(
			'ajax_url'          => admin_url( 'admin-ajax.php' ),
			'settings_page_url' => esc_url( admin_url( "admin.php?page={$menu_slug}-settings" ) ),
			'nonce'             => self::$settings::create_ajax_nonce()
		);
		$localize_script = $menu_slug . '-admin-settings';
		if ( in_array( $page, [ $menu_slug . '-imported', $menu_slug . '-import-list', $menu_slug, $menu_slug . '-settings' ] ) ) {
			self::$settings::enqueue_style(
				array(
					'semantic-ui-accordion',
					'semantic-ui-message',
					'semantic-ui-menu',
					'semantic-ui-tab',
				),
				array( 'accordion', 'message', 'menu', 'tab' ),
				array( 1, 1, 1, 1 )
			);
			self::$settings::enqueue_script(
				array(
					'semantic-ui-accordion',
					'semantic-ui-tab',
				),
				array( 'accordion', 'tab' ),
				array( 1, 1 )
			);
		}
		switch ( $page ) {
			case $menu_slug . '-error-images':
				wp_dequeue_style( $menu_slug . '-admin-settings' );
				self::$settings::enqueue_style(
					array( 'semantic-ui-message', $menu_slug . '-admin-error-images' ),
					array( 'message', 'error-images' ),
					array( 1 )
				);
				self::$settings::enqueue_script(
					array( $menu_slug . '-admin-error-images' ),
					array( 'error-images' ),
					array( 0 ),
					array( array( 'jquery' ) )
				);
				$localize_script = $menu_slug . '-admin-error-images';
				$params          = array_merge( $params, array(
					'i18n_confirm_delete'     => esc_html__( 'Are you sure you want to delete this item?', 'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_confirm_delete_all' => esc_html__( 'Are you sure you want to delete all item(s) on this page?', 'tmds-dropshipping-for-temu-and-woo' ),
				) );
				break;
			case $menu_slug . '-imported':
				self::$settings::enqueue_script(
					array( $menu_slug . '-admin-imported' ),
					array( 'imported' ),
					array( 0 ),
					array( array( 'jquery' ) )
				);
				$localize_script = $menu_slug . '-admin-imported';
				$params          = array_merge( $params, array(
					'check'    => esc_attr__( 'Check', 'tmds-dropshipping-for-temu-and-woo' ),
					'override' => esc_attr__( 'Override', 'tmds-dropshipping-for-temu-and-woo' ),
				) );
				break;
			case $menu_slug:
			case $menu_slug . '-import-list':
				self::$settings::enqueue_script(
					array( $menu_slug . '-admin-import-list' ),
					array( 'import-list' ),
					array( 0 ),
					array( array( 'jquery', 'jquery-ui-sortable' ) )
				);
				$localize_script = $menu_slug . '-admin-import-list';
				$params          = array_merge( $params, array(
					'i18n_bulk_import_product_confirm' => esc_html__( 'Import all selected product(s)?', 'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_bulk_remove_product_confirm' => esc_html__( 'Remove selected product(s) from import list?', 'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_remove_product_confirm'      => esc_html__( 'Remove this product from import list?', 'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_empty_variation_error'       => esc_html__( 'Please select at least 1 variation to import.', 'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_empty_price_error'           => esc_html__( 'Regular price can not be empty.', 'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_sale_price_error'            => esc_html__( 'Sale price must be smaller than regular price.', 'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_not_found_error'             => esc_html__( 'No product found.', 'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_import_all_confirm'          => esc_html__( 'Import all products on this page to your WooCommerce store?',
						'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_empty_attribute_name'        => esc_html__( 'Attribute name can not be empty', 'tmds-dropshipping-for-temu-and-woo' ),
					'i18n_invalid_attribute_values'    => esc_html__( 'Attribute value can not be empty or duplicated', 'tmds-dropshipping-for-temu-and-woo' ),
				) );
				break;
			case $menu_slug . '-settings':
				self::$settings::enqueue_script(
					array( $menu_slug . '-admin-settings' ),
					array( 'admin-settings' ),
					array( 0 ),
					array( array( 'jquery' ) )
				);
				break;
			default:
				self::$settings::enqueue_style(
					array( 'semantic-ui-accordion', 'semantic-ui-step', ),
					array( 'accordion', 'step' ),
					array( 1, 1 )
				);
				self::$settings::enqueue_script(
					array( 'semantic-ui-accordion', $menu_slug . '-admin-settings' ),
					array( 'accordion', 'admin-settings' ),
					array( 1, 0 )
				);
		}
		wp_localize_script( $localize_script, $menu_slug . '_params', $params );
	}

	public function add_ajax_events() {
		$prefix = self::$settings::$prefix;
		$events = [
			$prefix . '_search_cate'            => array(
				'function' => 'search_cate',
				'class'    => $this,
			),
			$prefix . '_search_tag'             => array(
				'function' => 'search_tags',
				'class'    => $this,
			),
			$prefix . '_setup_install_plugins'  => array(
				'function' => 'install_plugins',
				'class'    => TMDS_Admin_Class_Prefix . 'Setup_Wizard',
			),
			$prefix . '_setup_activate_plugins' => array(
				'function' => 'activate_plugins',
				'class'    => TMDS_Admin_Class_Prefix . 'Setup_Wizard',
			),
		];
		self::ajax_events( apply_filters( 'tmds_admin_ajax_events', $events, $prefix ) );
	}

	public static function ajax_events( $events ) {
		if ( ! is_array( $events ) || empty( $events ) ) {
			return;
		}
		foreach ( $events as $action => $arg ) {
			if ( ! isset( $arg['function'] ) ) {
				continue;
			}
			$class = $arg['class'] ?? __CLASS__;
			add_action( "wp_ajax_$action", [ $class, $arg['function'] ] );
			if ( ! empty( $arg['nopriv'] ) ) {
				add_action( "wp_ajax_nopriv_$action", [ $class, $arg['function'] ] );
			}
		}
	}

	public function search_cate() {
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( 'missing role' );
		}
		$action = 'admin_ajax';
		if ( apply_filters( 'tmds_verify_ajax_nonce', true, $action) &&
		     !check_ajax_referer( 'tmds_' . $action, 'tmds_nonce', false )) {
			wp_send_json_error( 'Invalid nonce' );
		}
		$keyword    = isset( $_GET['keyword'] ) ? sanitize_text_field( wp_unslash( $_GET['keyword'] ) ) : '';// phpcs:ignore WordPress.Security.NonceVerification.Recommended
		$categories = get_terms(
			array(
				'taxonomy'   => 'product_cat',
				'orderby'    => 'name',
				'order'      => 'ASC',
				'search'     => $keyword,
				'hide_empty' => false
			)
		);

		$items = array();

		if ( count( $categories ) ) {
			foreach ( $categories as $category ) {
				$item    = array(
					'id'   => $category->term_id,
					'text' => $category->name
				);
				$items[] = $item;
			}
		}

		wp_send_json( $items );
	}

	public function search_tags() {
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( 'missing role' );
		}
		$action = 'admin_ajax';
		if ( apply_filters( 'tmds_verify_ajax_nonce', true, $action) &&
		     !check_ajax_referer( 'tmds_' . $action, 'tmds_nonce', false )) {
			wp_send_json_error( 'Invalid nonce' );
		}
		$keyword = isset( $_GET['keyword'] ) ? sanitize_text_field( wp_unslash( $_GET['keyword'] ) ) : '';// phpcs:ignore WordPress.Security.NonceVerification.Recommended
		$terms   = get_terms(
			array(
				'taxonomy'   => 'product_tag',
				'orderby'    => 'name',
				'order'      => 'ASC',
				'search'     => $keyword,
				'hide_empty' => false
			)
		);

		$items = array();

		if ( count( $terms ) ) {
			foreach ( $terms as $term ) {
				$item    = array(
					'id'   => $term->term_id,
					'text' => $term->name
				);
				$items[] = $item;
			}
		}

		wp_send_json( $items );
	}
}