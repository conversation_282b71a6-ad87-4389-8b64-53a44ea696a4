!function(C,n,A,S){"use strict";n=void 0!==n&&n.Math==Math?n:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),C.fn.transition=function(){var u,r=C(this),p=r.selector||"",g=(new Date).getTime(),v=[],b=arguments,y=b[0],h=[].slice.call(arguments,1),w="string"==typeof y;n.requestAnimationFrame||n.mozRequestAnimationFrame||n.webkitRequestAnimationFrame||n.msRequestAnimationFrame;return r.each(function(t){var d,s,e,c,i,a,n,o,m=C(this),l=this,f={initialize:function(){d=f.get.settings.apply(l,b),c=d.className,e=d.error,i=d.metadata,o="."+d.namespace,n="module-"+d.namespace,s=m.data(n)||f,a=f.get.animationEndEvent(),!1===(w=w&&f.invoke(y))&&(f.verbose("Converted arguments into settings object",d),d.interval?f.delay(d.animate):f.animate(),f.instantiate())},instantiate:function(){f.verbose("Storing instance of module",f),s=f,m.data(n,s)},destroy:function(){f.verbose("Destroying previous module for",l),m.removeData(n)},refresh:function(){f.verbose("Refreshing display type on next animation"),delete f.displayType},forceRepaint:function(){f.verbose("Forcing element repaint");var n=m.parent(),e=m.next();0===e.length?m.detach().appendTo(n):m.detach().insertBefore(e)},repaint:function(){f.verbose("Repainting element");l.offsetWidth},delay:function(n){var e,i=(i=f.get.animationDirection())||(f.can.transition()?f.get.direction():"static");n=n!==S?n:d.interval,e="auto"==d.reverse&&i==c.outward||1==d.reverse?(r.length-t)*d.interval:t*d.interval,f.debug("Delaying animation by",e),setTimeout(f.animate,e)},animate:function(n){if(d=n||d,!f.is.supported())return f.error(e.support),!1;if(f.debug("Preparing animation",d.animation),f.is.animating()){if(d.queue)return!d.allowRepeats&&f.has.direction()&&f.is.occurring()&&!0!==f.queuing?f.debug("Animation is currently occurring, preventing queueing same animation",d.animation):f.queue(d.animation),!1;if(!d.allowRepeats&&f.is.occurring())return f.debug("Animation is already occurring, will not execute repeated animation",d.animation),!1;f.debug("New animation started, completing previous early",d.animation),s.complete()}f.can.animate()?f.set.animating(d.animation):f.error(e.noAnimation,d.animation,l)},reset:function(){f.debug("Resetting animation to beginning conditions"),f.remove.animationCallbacks(),f.restore.conditions(),f.remove.animating()},queue:function(n){f.debug("Queueing animation of",n),f.queuing=!0,m.one(a+".queue"+o,function(){f.queuing=!1,f.repaint(),f.animate.apply(this,d)})},complete:function(n){f.debug("Animation complete",d.animation),f.remove.completeCallback(),f.remove.failSafe(),f.is.looping()||(f.is.outward()?(f.verbose("Animation is outward, hiding element"),f.restore.conditions(),f.hide()):f.is.inward()?(f.verbose("Animation is outward, showing element"),f.restore.conditions(),f.show()):(f.verbose("Static animation completed"),f.restore.conditions(),d.onComplete.call(l)))},force:{visible:function(){var n=m.attr("style"),e=f.get.userStyle(),i=f.get.displayType(),t=e+"display: "+i+" !important;",a=m.css("display"),o=n===S||""===n;a!==i?(f.verbose("Overriding default display to show element",i),m.attr("style",t)):o&&m.removeAttr("style")},hidden:function(){var n=m.attr("style"),e=m.css("display"),i=n===S||""===n;"none"===e||f.is.hidden()?i&&m.removeAttr("style"):(f.verbose("Overriding default display to hide element"),m.css("display","none"))}},has:{direction:function(n){var i=!1;return"string"==typeof(n=n||d.animation)&&(n=n.split(" "),C.each(n,function(n,e){e!==c.inward&&e!==c.outward||(i=!0)})),i},inlineDisplay:function(){var n=m.attr("style")||"";return C.isArray(n.match(/display.*?;/,""))}},set:{animating:function(n){var e;f.remove.completeCallback(),n=n||d.animation,e=f.get.animationClass(n),f.save.animation(e),f.force.visible(),f.remove.hidden(),f.remove.direction(),f.start.animation(e)},duration:function(n,e){!(e="number"==typeof(e=e||d.duration)?e+"ms":e)&&0!==e||(f.verbose("Setting animation duration",e),m.css({"animation-duration":e}))},direction:function(n){(n=n||f.get.direction())==c.inward?f.set.inward():f.set.outward()},looping:function(){f.debug("Transition set to loop"),m.addClass(c.looping)},hidden:function(){m.addClass(c.transition).addClass(c.hidden)},inward:function(){f.debug("Setting direction to inward"),m.removeClass(c.outward).addClass(c.inward)},outward:function(){f.debug("Setting direction to outward"),m.removeClass(c.inward).addClass(c.outward)},visible:function(){m.addClass(c.transition).addClass(c.visible)}},start:{animation:function(n){n=n||f.get.animationClass(),f.debug("Starting tween",n),m.addClass(n).one(a+".complete"+o,f.complete),d.useFailSafe&&f.add.failSafe(),f.set.duration(d.duration),d.onStart.call(l)}},save:{animation:function(n){f.cache||(f.cache={}),f.cache.animation=n},displayType:function(n){"none"!==n&&m.data(i.displayType,n)},transitionExists:function(n,e){C.fn.transition.exists[n]=e,f.verbose("Saving existence of transition",n,e)}},restore:{conditions:function(){var n=f.get.currentAnimation();n&&(m.removeClass(n),f.verbose("Removing animation class",f.cache)),f.remove.duration()}},add:{failSafe:function(){var n=f.get.duration();f.timer=setTimeout(function(){m.triggerHandler(a)},n+d.failSafeDelay),f.verbose("Adding fail safe timer",f.timer)}},remove:{animating:function(){m.removeClass(c.animating)},animationCallbacks:function(){f.remove.queueCallback(),f.remove.completeCallback()},queueCallback:function(){m.off(".queue"+o)},completeCallback:function(){m.off(".complete"+o)},display:function(){m.css("display","")},direction:function(){m.removeClass(c.inward).removeClass(c.outward)},duration:function(){m.css("animation-duration","")},failSafe:function(){f.verbose("Removing fail safe timer",f.timer),f.timer&&clearTimeout(f.timer)},hidden:function(){m.removeClass(c.hidden)},visible:function(){m.removeClass(c.visible)},looping:function(){f.debug("Transitions are no longer looping"),f.is.looping()&&(f.reset(),m.removeClass(c.looping))},transition:function(){m.removeClass(c.visible).removeClass(c.hidden)}},get:{settings:function(n,e,i){return"object"==typeof n?C.extend(!0,{},C.fn.transition.settings,n):"function"==typeof i?C.extend({},C.fn.transition.settings,{animation:n,onComplete:i,duration:e}):"string"==typeof e||"number"==typeof e?C.extend({},C.fn.transition.settings,{animation:n,duration:e}):"object"==typeof e?C.extend({},C.fn.transition.settings,e,{animation:n}):"function"==typeof e?C.extend({},C.fn.transition.settings,{animation:n,onComplete:e}):C.extend({},C.fn.transition.settings,{animation:n})},animationClass:function(n){var e=n||d.animation,i=f.can.transition()&&!f.has.direction()?f.get.direction()+" ":"";return c.animating+" "+c.transition+" "+i+e},currentAnimation:function(){return!(!f.cache||f.cache.animation===S)&&f.cache.animation},currentDirection:function(){return f.is.inward()?c.inward:c.outward},direction:function(){return f.is.hidden()||!f.is.visible()?c.inward:c.outward},animationDirection:function(n){var i;return"string"==typeof(n=n||d.animation)&&(n=n.split(" "),C.each(n,function(n,e){e===c.inward?i=c.inward:e===c.outward&&(i=c.outward)})),i||!1},duration:function(n){return!1===(n=n||d.duration)&&(n=m.css("animation-duration")||0),"string"==typeof n?-1<n.indexOf("ms")?parseFloat(n):1e3*parseFloat(n):n},displayType:function(n){return n=n===S||n,d.displayType?d.displayType:(n&&m.data(i.displayType)===S&&f.can.transition(!0),m.data(i.displayType))},userStyle:function(n){return(n=n||m.attr("style")||"").replace(/display.*?;/,"")},transitionExists:function(n){return C.fn.transition.exists[n]},animationStartEvent:function(){var n,e=A.createElement("div"),i={animation:"animationstart",OAnimation:"oAnimationStart",MozAnimation:"mozAnimationStart",WebkitAnimation:"webkitAnimationStart"};for(n in i)if(e.style[n]!==S)return i[n];return!1},animationEndEvent:function(){var n,e=A.createElement("div"),i={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"mozAnimationEnd",WebkitAnimation:"webkitAnimationEnd"};for(n in i)if(e.style[n]!==S)return i[n];return!1}},can:{transition:function(n){var e,i,t,a,o,r,s=d.animation,l=f.get.transitionExists(s),u=f.get.displayType(!1);if(l===S||n){if(f.verbose("Determining whether animation exists"),e=m.attr("class"),i=m.prop("tagName"),a=(t=C("<"+i+" />").addClass(e).insertAfter(m)).addClass(s).removeClass(c.inward).removeClass(c.outward).addClass(c.animating).addClass(c.transition).css("animationName"),o=t.addClass(c.inward).css("animationName"),u||(u=t.attr("class",e).removeAttr("style").removeClass(c.hidden).removeClass(c.visible).show().css("display"),f.verbose("Determining final display state",u),f.save.displayType(u)),t.remove(),a!=o)f.debug("Direction exists for animation",s),r=!0;else{if("none"==a||!a)return void f.debug("No animation defined in css",s);f.debug("Static animation found",s,u),r=!1}f.save.transitionExists(s,r)}return l!==S?l:r},animate:function(){return f.can.transition()!==S}},is:{animating:function(){return m.hasClass(c.animating)},inward:function(){return m.hasClass(c.inward)},outward:function(){return m.hasClass(c.outward)},looping:function(){return m.hasClass(c.looping)},occurring:function(n){return n="."+(n=n||d.animation).replace(" ","."),0<m.filter(n).length},visible:function(){return m.is(":visible")},hidden:function(){return"hidden"===m.css("visibility")},supported:function(){return!1!==a}},hide:function(){f.verbose("Hiding element"),f.is.animating()&&f.reset(),l.blur(),f.remove.display(),f.remove.visible(),f.set.hidden(),f.force.hidden(),d.onHide.call(l),d.onComplete.call(l)},show:function(n){f.verbose("Showing element",n),f.remove.hidden(),f.set.visible(),f.force.visible(),d.onShow.call(l),d.onComplete.call(l)},toggle:function(){f.is.visible()?f.hide():f.show()},stop:function(){f.debug("Stopping current animation"),m.triggerHandler(a)},stopAll:function(){f.debug("Stopping all animation"),f.remove.queueCallback(),m.triggerHandler(a)},clear:{queue:function(){f.debug("Clearing animation queue"),f.remove.queueCallback()}},enable:function(){f.verbose("Starting animation"),m.removeClass(c.disabled)},disable:function(){f.debug("Stopping animation"),m.addClass(c.disabled)},setting:function(n,e){if(f.debug("Changing setting",n,e),C.isPlainObject(n))C.extend(!0,d,n);else{if(e===S)return d[n];C.isPlainObject(d[n])?C.extend(!0,d[n],e):d[n]=e}},internal:function(n,e){if(C.isPlainObject(n))C.extend(!0,f,n);else{if(e===S)return f[n];f[n]=e}},debug:function(){!d.silent&&d.debug&&(d.performance?f.performance.log(arguments):(f.debug=Function.prototype.bind.call(console.info,console,d.name+":"),f.debug.apply(console,arguments)))},verbose:function(){!d.silent&&d.verbose&&d.debug&&(d.performance?f.performance.log(arguments):(f.verbose=Function.prototype.bind.call(console.info,console,d.name+":"),f.verbose.apply(console,arguments)))},error:function(){d.silent||(f.error=Function.prototype.bind.call(console.error,console,d.name+":"),f.error.apply(console,arguments))},performance:{log:function(n){var e,i;d.performance&&(i=(e=(new Date).getTime())-(g||e),g=e,v.push({Name:n[0],Arguments:[].slice.call(n,1)||"",Element:l,"Execution Time":i})),clearTimeout(f.performance.timer),f.performance.timer=setTimeout(f.performance.display,500)},display:function(){var n=d.name+":",i=0;g=!1,clearTimeout(f.performance.timer),C.each(v,function(n,e){i+=e["Execution Time"]}),n+=" "+i+"ms",p&&(n+=" '"+p+"'"),1<r.length&&(n+=" ("+r.length+")"),(console.group!==S||console.table!==S)&&0<v.length&&(console.groupCollapsed(n),console.table?console.table(v):C.each(v,function(n,e){console.log(e.Name+": "+e["Execution Time"]+"ms")}),console.groupEnd()),v=[]}},invoke:function(t,n,e){var a,o,i,r=s;return n=n||h,e=l||e,"string"==typeof t&&r!==S&&(t=t.split(/[\. ]/),a=t.length-1,C.each(t,function(n,e){var i=n!=a?e+t[n+1].charAt(0).toUpperCase()+t[n+1].slice(1):t;if(C.isPlainObject(r[i])&&n!=a)r=r[i];else{if(r[i]!==S)return o=r[i],!1;if(!C.isPlainObject(r[e])||n==a)return r[e]!==S&&(o=r[e]),!1;r=r[e]}})),C.isFunction(o)?i=o.apply(e,n):o!==S&&(i=o),C.isArray(u)?u.push(i):u!==S?u=[u,i]:i!==S&&(u=i),o!==S&&o}};f.initialize()}),u!==S?u:this},C.fn.transition.exists={},C.fn.transition.settings={name:"Transition",silent:!1,debug:!1,verbose:!1,performance:!0,namespace:"transition",interval:0,reverse:"auto",onStart:function(){},onComplete:function(){},onShow:function(){},onHide:function(){},useFailSafe:!0,failSafeDelay:100,allowRepeats:!1,displayType:!1,animation:"fade",duration:!1,queue:!0,metadata:{displayType:"display"},className:{animating:"animating",disabled:"disabled",hidden:"hidden",inward:"in",loading:"loading",looping:"looping",outward:"out",transition:"transition",visible:"visible"},error:{noAnimation:"Element is no longer attached to DOM. Unable to animate.  Use silent setting to surpress this warning in production.",repeated:"That animation is already occurring, cancelling repeated animation",method:"The method you called is not defined",support:"This browser does not support CSS animations"}}}(jQuery,window,document);