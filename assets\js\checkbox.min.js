!function(D,S,E,O){"use strict";S=void 0!==S&&S.Math==Math?S:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),D.fn.checkbox=function(k){var m,e=D(this),v=e.selector||"",y=(new Date).getTime(),C=[],x=k,w="string"==typeof x,I=[].slice.call(arguments,1);return e.each(function(){var e,i=D.extend(!0,{},D.fn.checkbox.settings,k),n=i.className,t=i.namespace,o=i.selector,r=i.error,a="."+t,c="module-"+t,l=D(this),d=D(this).children(o.label),s=D(this).children(o.input),u=s[0],b=!1,h=!1,g=l.data(c),f=this,p={initialize:function(){p.verbose("Initializing checkbox",i),p.create.label(),p.bind.events(),p.set.tabbable(),p.hide.input(),p.observeChanges(),p.instantiate(),p.setup()},instantiate:function(){p.verbose("Storing instance of module",p),g=p,l.data(c,p)},destroy:function(){p.verbose("Destroying module"),p.unbind.events(),p.show.input(),l.removeData(c)},fix:{reference:function(){l.is(o.input)&&(p.debug("Behavior called on <input> adjusting invoked element"),l=l.closest(o.checkbox),p.refresh())}},setup:function(){p.set.initialLoad(),p.is.indeterminate()?(p.debug("Initial value is indeterminate"),p.indeterminate()):p.is.checked()?(p.debug("Initial value is checked"),p.check()):(p.debug("Initial value is unchecked"),p.uncheck()),p.remove.initialLoad()},refresh:function(){d=l.children(o.label),s=l.children(o.input),u=s[0]},hide:{input:function(){p.verbose("Modifying <input> z-index to be unselectable"),s.addClass(n.hidden)}},show:{input:function(){p.verbose("Modifying <input> z-index to be selectable"),s.removeClass(n.hidden)}},observeChanges:function(){"MutationObserver"in S&&((e=new MutationObserver(function(e){p.debug("DOM tree modified, updating selector cache"),p.refresh()})).observe(f,{childList:!0,subtree:!0}),p.debug("Setting up mutation observer",e))},attachEvents:function(e,n){var t=D(e);n=D.isFunction(p[n])?p[n]:p.toggle,0<t.length?(p.debug("Attaching checkbox events to element",e,n),t.on("click"+a,n)):p.error(r.notFound)},event:{click:function(e){var n=D(e.target);n.is(o.input)?p.verbose("Using default check action on initialized checkbox"):n.is(o.link)?p.debug("Clicking link inside checkbox, skipping toggle"):(p.toggle(),s.focus(),e.preventDefault())},keydown:function(e){var n=e.which,t=13,i=32;h=n==27?(p.verbose("Escape key pressed blurring field"),s.blur(),!0):!(e.ctrlKey||n!=i&&n!=t)&&(p.verbose("Enter/space key pressed, toggling checkbox"),p.toggle(),!0)},keyup:function(e){h&&e.preventDefault()}},check:function(){p.should.allowCheck()&&(p.debug("Checking checkbox",s),p.set.checked(),p.should.ignoreCallbacks()||(i.onChecked.call(u),i.onChange.call(u)))},uncheck:function(){p.should.allowUncheck()&&(p.debug("Unchecking checkbox"),p.set.unchecked(),p.should.ignoreCallbacks()||(i.onUnchecked.call(u),i.onChange.call(u)))},indeterminate:function(){p.should.allowIndeterminate()?p.debug("Checkbox is already indeterminate"):(p.debug("Making checkbox indeterminate"),p.set.indeterminate(),p.should.ignoreCallbacks()||(i.onIndeterminate.call(u),i.onChange.call(u)))},determinate:function(){p.should.allowDeterminate()?p.debug("Checkbox is already determinate"):(p.debug("Making checkbox determinate"),p.set.determinate(),p.should.ignoreCallbacks()||(i.onDeterminate.call(u),i.onChange.call(u)))},enable:function(){p.is.enabled()?p.debug("Checkbox is already enabled"):(p.debug("Enabling checkbox"),p.set.enabled(),i.onEnable.call(u),i.onEnabled.call(u))},disable:function(){p.is.disabled()?p.debug("Checkbox is already disabled"):(p.debug("Disabling checkbox"),p.set.disabled(),i.onDisable.call(u),i.onDisabled.call(u))},get:{radios:function(){var e=p.get.name();return D('input[name="'+e+'"]').closest(o.checkbox)},otherRadios:function(){return p.get.radios().not(l)},name:function(){return s.attr("name")}},is:{initialLoad:function(){return b},radio:function(){return s.hasClass(n.radio)||"radio"==s.attr("type")},indeterminate:function(){return s.prop("indeterminate")!==O&&s.prop("indeterminate")},checked:function(){return s.prop("checked")!==O&&s.prop("checked")},disabled:function(){return s.prop("disabled")!==O&&s.prop("disabled")},enabled:function(){return!p.is.disabled()},determinate:function(){return!p.is.indeterminate()},unchecked:function(){return!p.is.checked()}},should:{allowCheck:function(){return p.is.determinate()&&p.is.checked()&&!p.should.forceCallbacks()?(p.debug("Should not allow check, checkbox is already checked"),!1):!1!==i.beforeChecked.apply(u)||(p.debug("Should not allow check, beforeChecked cancelled"),!1)},allowUncheck:function(){return p.is.determinate()&&p.is.unchecked()&&!p.should.forceCallbacks()?(p.debug("Should not allow uncheck, checkbox is already unchecked"),!1):!1!==i.beforeUnchecked.apply(u)||(p.debug("Should not allow uncheck, beforeUnchecked cancelled"),!1)},allowIndeterminate:function(){return p.is.indeterminate()&&!p.should.forceCallbacks()?(p.debug("Should not allow indeterminate, checkbox is already indeterminate"),!1):!1!==i.beforeIndeterminate.apply(u)||(p.debug("Should not allow indeterminate, beforeIndeterminate cancelled"),!1)},allowDeterminate:function(){return p.is.determinate()&&!p.should.forceCallbacks()?(p.debug("Should not allow determinate, checkbox is already determinate"),!1):!1!==i.beforeDeterminate.apply(u)||(p.debug("Should not allow determinate, beforeDeterminate cancelled"),!1)},forceCallbacks:function(){return p.is.initialLoad()&&i.fireOnInit},ignoreCallbacks:function(){return b&&!i.fireOnInit}},can:{change:function(){return!(l.hasClass(n.disabled)||l.hasClass(n.readOnly)||s.prop("disabled")||s.prop("readonly"))},uncheck:function(){return"boolean"==typeof i.uncheckable?i.uncheckable:!p.is.radio()}},set:{initialLoad:function(){b=!0},checked:function(){p.verbose("Setting class to checked"),l.removeClass(n.indeterminate).addClass(n.checked),p.is.radio()&&p.uncheckOthers(),p.is.indeterminate()||!p.is.checked()?(p.verbose("Setting state to checked",u),s.prop("indeterminate",!1).prop("checked",!0),p.trigger.change()):p.debug("Input is already checked, skipping input property change")},unchecked:function(){p.verbose("Removing checked class"),l.removeClass(n.indeterminate).removeClass(n.checked),p.is.indeterminate()||!p.is.unchecked()?(p.debug("Setting state to unchecked"),s.prop("indeterminate",!1).prop("checked",!1),p.trigger.change()):p.debug("Input is already unchecked")},indeterminate:function(){p.verbose("Setting class to indeterminate"),l.addClass(n.indeterminate),p.is.indeterminate()?p.debug("Input is already indeterminate, skipping input property change"):(p.debug("Setting state to indeterminate"),s.prop("indeterminate",!0),p.trigger.change())},determinate:function(){p.verbose("Removing indeterminate class"),l.removeClass(n.indeterminate),p.is.determinate()?p.debug("Input is already determinate, skipping input property change"):(p.debug("Setting state to determinate"),s.prop("indeterminate",!1))},disabled:function(){p.verbose("Setting class to disabled"),l.addClass(n.disabled),p.is.disabled()?p.debug("Input is already disabled, skipping input property change"):(p.debug("Setting state to disabled"),s.prop("disabled","disabled"),p.trigger.change())},enabled:function(){p.verbose("Removing disabled class"),l.removeClass(n.disabled),p.is.enabled()?p.debug("Input is already enabled, skipping input property change"):(p.debug("Setting state to enabled"),s.prop("disabled",!1),p.trigger.change())},tabbable:function(){p.verbose("Adding tabindex to checkbox"),s.attr("tabindex")===O&&s.attr("tabindex",0)}},remove:{initialLoad:function(){b=!1}},trigger:{change:function(){var e=E.createEvent("HTMLEvents"),n=s[0];n&&(p.verbose("Triggering native change event"),e.initEvent("change",!0,!1),n.dispatchEvent(e))}},create:{label:function(){0<s.prevAll(o.label).length?(s.prev(o.label).detach().insertAfter(s),p.debug("Moving existing label",d)):p.has.label()||(d=D("<label>").insertAfter(s),p.debug("Creating label",d))}},has:{label:function(){return 0<d.length}},bind:{events:function(){p.verbose("Attaching checkbox events"),l.on("click"+a,p.event.click).on("keydown"+a,o.input,p.event.keydown).on("keyup"+a,o.input,p.event.keyup)}},unbind:{events:function(){p.debug("Removing events"),l.off(a)}},uncheckOthers:function(){var e=p.get.otherRadios();p.debug("Unchecking other radios",e),e.removeClass(n.checked)},toggle:function(){p.can.change()?p.is.indeterminate()||p.is.unchecked()?(p.debug("Currently unchecked"),p.check()):p.is.checked()&&p.can.uncheck()&&(p.debug("Currently checked"),p.uncheck()):p.is.radio()||p.debug("Checkbox is read-only or disabled, ignoring toggle")},setting:function(e,n){if(p.debug("Changing setting",e,n),D.isPlainObject(e))D.extend(!0,i,e);else{if(n===O)return i[e];D.isPlainObject(i[e])?D.extend(!0,i[e],n):i[e]=n}},internal:function(e,n){if(D.isPlainObject(e))D.extend(!0,p,e);else{if(n===O)return p[e];p[e]=n}},debug:function(){!i.silent&&i.debug&&(i.performance?p.performance.log(arguments):(p.debug=Function.prototype.bind.call(console.info,console,i.name+":"),p.debug.apply(console,arguments)))},verbose:function(){!i.silent&&i.verbose&&i.debug&&(i.performance?p.performance.log(arguments):(p.verbose=Function.prototype.bind.call(console.info,console,i.name+":"),p.verbose.apply(console,arguments)))},error:function(){i.silent||(p.error=Function.prototype.bind.call(console.error,console,i.name+":"),p.error.apply(console,arguments))},performance:{log:function(e){var n,t;i.performance&&(t=(n=(new Date).getTime())-(y||n),y=n,C.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:f,"Execution Time":t})),clearTimeout(p.performance.timer),p.performance.timer=setTimeout(p.performance.display,500)},display:function(){var e=i.name+":",t=0;y=!1,clearTimeout(p.performance.timer),D.each(C,function(e,n){t+=n["Execution Time"]}),e+=" "+t+"ms",v&&(e+=" '"+v+"'"),(console.group!==O||console.table!==O)&&0<C.length&&(console.groupCollapsed(e),console.table?console.table(C):D.each(C,function(e,n){console.log(n.Name+": "+n["Execution Time"]+"ms")}),console.groupEnd()),C=[]}},invoke:function(i,e,n){var o,a,t,c=g;return e=e||I,n=f||n,"string"==typeof i&&c!==O&&(i=i.split(/[\. ]/),o=i.length-1,D.each(i,function(e,n){var t=e!=o?n+i[e+1].charAt(0).toUpperCase()+i[e+1].slice(1):i;if(D.isPlainObject(c[t])&&e!=o)c=c[t];else{if(c[t]!==O)return a=c[t],!1;if(!D.isPlainObject(c[n])||e==o)return c[n]!==O?a=c[n]:p.error(r.method,i),!1;c=c[n]}})),D.isFunction(a)?t=a.apply(n,e):a!==O&&(t=a),D.isArray(m)?m.push(t):m!==O?m=[m,t]:t!==O&&(m=t),a}};w?(g===O&&p.initialize(),p.invoke(x)):(g!==O&&g.invoke("destroy"),p.initialize())}),m!==O?m:this},D.fn.checkbox.settings={name:"Checkbox",namespace:"checkbox",silent:!1,debug:!1,verbose:!0,performance:!0,uncheckable:"auto",fireOnInit:!1,onChange:function(){},beforeChecked:function(){},beforeUnchecked:function(){},beforeDeterminate:function(){},beforeIndeterminate:function(){},onChecked:function(){},onUnchecked:function(){},onDeterminate:function(){},onIndeterminate:function(){},onEnable:function(){},onDisable:function(){},onEnabled:function(){},onDisabled:function(){},className:{checked:"checked",indeterminate:"indeterminate",disabled:"disabled",hidden:"hidden",radio:"radio",readOnly:"read-only"},error:{method:"The method you called is not defined"},selector:{checkbox:".vi-ui.checkbox",label:"label, .box",input:'input[type="checkbox"], input[type="radio"]',link:"a[href]"}}}(jQuery,window,document);