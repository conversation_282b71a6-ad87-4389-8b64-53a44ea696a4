<?php

defined( 'ABSPATH' ) || exit;


class TMDS_Admin_Product {
	private static $settings;

	public function __construct() {
		self::$settings = TMDS_DATA::get_instance();
		add_filter( 'post_row_actions', array( $this, 'post_row_actions' ), 20, 2 );
		add_action( 'add_meta_boxes', array( $this, 'product_info_meta_box' ) );
		add_action( 'admin_print_styles', array( $this, 'admin_print_styles' ) );
		add_action( 'transition_post_status', array( $this, 'transition_post_status' ), 10, 3 );
		add_action( 'deleted_post', array( $this, 'deleted_post' ) );
	}

	/**Set a product status
	 *
	 * @param $product_id
	 * @param string $status
	 */
	public function set_status( $product_id, $status = 'trash' ) {
		$import_id= self::$settings::get_temu_pd_id($product_id);
		if ( $import_id ) {
			$prefix = self::$settings::$prefix ;
			$args    = array(
				'tmds_query'          => 1,
				'post_type'      => $prefix . '_draft_product',
				'order'          => 'DESC',
				'fields'         => 'ids',
				'post_status'          => ['publish', 'draft', 'override','trash'],
				'posts_per_page' => 1,
				'meta_query' => [// phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_query
					'relation' => 'and',
					[
						'key'     => '_' . $prefix . '_woo_id',
						'compare' => '=',
						'value' => $product_id,
					]
				],
			);
			$the_query   = TMDS_Post::query( $args );
			$ids = $the_query->get_posts();
			wp_reset_postdata();
			$id = $ids[0] ?? '';
			if ( $id  && is_numeric($id)) {
				TMDS_Post::update_post( array( 'ID' => $id, 'post_status' => $status ) );
			}
		}
	}

	/**Set a product status to trash when a WC product is deleted
	 *
	 * @param $product_id
	 */
	public function deleted_post( $product_id ) {
		$this->set_status( $product_id, 'trash' );
	}

	/**Set a product status to trash when a WC product is trashed and set to publish when a trashed product is restored
	 *
	 * @param $new_status
	 * @param $old_status
	 * @param $post
	 */
	public function transition_post_status( $new_status, $old_status, $post ) {
		if ( 'product' === $post->post_type ) {
			$product_id = $post->ID;
			if ( 'trash' === $new_status ) {
				$this->set_status( $product_id );
			} elseif ( $old_status === 'trash' ) {
				$this->set_status( $product_id, 'publish' );
			}
		}
	}

	/**
	 * @param $page
	 */
	public function admin_print_styles() {
		global $post_type;
		if ( $post_type !== 'product' ) {
			return;
		}
		$prefix =  self::$settings::$prefix;
		?>
		<style id="<?php echo esc_attr('villatheme-inline-css-'.$prefix)?>">
            <?php echo wp_kses("#{$prefix}_product_info .{$prefix}-video-shortcode,#{$prefix}_product_info .{$prefix}-view-original-product-button a{width:100%;text-align:center;cursor:pointer}", self::$settings::filter_allowed_html()) ?>
        </style>
		<?php
	}
	public function add_meta_box_callback( $post ) {
		$product_id        = $post->ID;
		$import_product_id = self::$settings::get_temu_pd_id($product_id);
		$import_url        = self::$settings::get_temu_pd_url( $product_id ,true);
        $prefix =  self::$settings::$prefix;
		if ( $import_url ) {
			printf( "<p>%s <a target='_blank' href='%s'>%s</a></p>",
                esc_html__( 'External ID', 'tmds-dropshipping-for-temu-and-woo' ),
                esc_url( $import_url ), esc_html( $import_product_id ) );
		}

		printf( "<p class='%s-view-original-product-button'><a target='_blank' class='button' href='%s'>%s</a></p>",
            esc_html($prefix),
			esc_url( admin_url( "admin.php?page={$prefix}-imported&{$prefix}_search_woo_id={$product_id}" ) ),
			esc_html__( 'View on Imported page', 'tmds-dropshipping-for-temu-and-woo' ) );
	}
	public function product_info_meta_box() {
		global $post;
		$product_id = $post->ID ??'';
		if ( self::$settings::get_temu_pd_id($product_id) ) {
			add_meta_box(
				self::$settings::$prefix.'_product_info',
				esc_html__( 'Temu product info', 'tmds-dropshipping-for-temu-and-woo' ),
				[ $this, 'add_meta_box_callback' ],
				'product', 'side', 'high'
			);
		}
	}
	/**
	 * @param $actions
	 * @param $post
	 *
	 * @return mixed
	 */
	public function post_row_actions( $actions, $post ) {
		if ( $post && $post->post_type === 'product' && $post->post_status !== 'trash' ) {
			$import_url = self::$settings::get_temu_pd_url( $post->ID,true );
			if ( $import_url ) {
                $prefix = self::$settings::$prefix;
				$actions[$prefix.'_view_on_temu'] = sprintf( '<a href="%s" target="_blank">%s</a>', esc_url( $import_url ),
                    esc_html__( 'View product on Temu', 'tmds-dropshipping-for-temu-and-woo' ) );
				$actions[$prefix.'_view_on_imported_page'] = sprintf( '<a href="%s" target="_blank">%s</a>',
					esc_url( admin_url( "admin.php?page={$prefix}-imported&{$prefix}_search_woo_id={$post->ID}" ) ),
					esc_html__( 'View product on Imported', 'tmds-dropshipping-for-temu-and-woo' ) );
			}

		}

		return $actions;
	}
}
