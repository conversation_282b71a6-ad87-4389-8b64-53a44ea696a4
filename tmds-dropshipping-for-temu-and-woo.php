<?php
/**
 * Plugin Name: TMDS - Dropshipping for TEMU and Woo
 * Plugin URI: https://villatheme.com/extensions/tmds-dropshipping-for-temu-and-woo/
 * Description: Effortlessly transfer product data from Temu to WooCommerce with ease
 * Version: 1.0.4
 * Author: VillaTheme
 * Author URI: https://villatheme.com
 * License:  GPL v2 or later
 * License URI:  https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: tmds-dropshipping-for-temu-and-woo
 * Domain Path: /languages
 * Copyright 2025 VillaTheme.com. All rights reserved.
 * Requires Plugins: woocommerce
 * Requires PHP: 7.0
 * Requires at least: 6.2
 * Tested up to: 6.8
 * WC tested up to: 10.0
 **/
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
if ( ! defined( 'TMDS_VERSION' ) ) {
	define( 'TMDS_VERSION', '1.0.4' );
	define( 'TMDS_NAME', 'TMDS - Dropshipping for TEMU and Woo' );
	define( 'TMDS_BASENAME', plugin_basename( __FILE__ ) );
	define( 'TMDS_DIR', plugin_dir_path( __FILE__ ) );
	define( 'TMDS_LANGUAGES', TMDS_DIR . "languages" . DIRECTORY_SEPARATOR );
	define( 'TMDS_INCLUDES', TMDS_DIR . "includes" . DIRECTORY_SEPARATOR );
	define( 'TMDS_ADMIN', TMDS_DIR . "admin" . DIRECTORY_SEPARATOR );
	define( 'TMDS_FRONTEND', TMDS_DIR . "frontend" . DIRECTORY_SEPARATOR );
	define( 'TMDS_TEMPLATES', TMDS_DIR . "templates" . DIRECTORY_SEPARATOR );
	define( 'TMDS_PLUGINS', TMDS_DIR . "plugins" . DIRECTORY_SEPARATOR );
	$plugin_url = plugins_url( 'assets/', __FILE__ );
	define( 'TMDS_CSS', $plugin_url . "css/" );
	define( 'TMDS_JS', $plugin_url . "js/" );
	define( 'TMDS_IMAGES', $plugin_url . "images/" );
	define( 'TMDS_Admin_Class_Prefix', "TMDS_Admin_" );
	define( 'TMDS_Frontend_Class_Prefix', "TMDS_Frontend_" );
}

if ( ! class_exists( 'TMDS' ) ) {
	class VITMDS_Init {
		public function __construct() {
			//Compatible with High-Performance order storage (COT)
			add_action( 'before_woocommerce_init', array( $this, 'before_woocommerce_init' ) );
			add_action( 'activated_plugin', array( $this, 'after_activated' ), 10, 2 );
			add_action( 'plugins_loaded', array( $this, 'check_environment' ) );
		}
        public function has_pro(){
            return defined('TMDSPRO_VERSION');
        }
		public function after_activated( $plugin, $network_wide ) {
			if ( $plugin !== TMDS_BASENAME || $this->has_pro()) {
				return;
			}
			global $wpdb;
			if ( function_exists( 'is_multisite' ) && is_multisite() && $network_wide ) {
				$current_blog = $wpdb->blogid;
				$blogs        = $wpdb->get_col( $wpdb->prepare('SELECT blog_id FROM %i',[$wpdb->blogs]) );// phpcs:ignore WordPress.DB.DirectDatabaseQuery.DirectQuery, WordPress.DB.DirectDatabaseQuery.NoCaching

				//Multi site activate action
				foreach ( $blogs as $blog ) {
					switch_to_blog( $blog );
					$this->create_table();
				}
				switch_to_blog( $current_blog );
			} else {
				//Single site activate action
				$this->create_table();
			}
			if ( ! get_option( 'tmds_params' ) ) {
				update_option( 'tmds_setup_wizard', 1, 'no' );
				$this->check_environment(  );
			}
		}
		public function create_table() {
			if ( ! class_exists( 'TMDS_Error_Images_Table' ) ) {
				require_once TMDS_INCLUDES . "class" . DIRECTORY_SEPARATOR . 'error-images-table.php';
			}
			if ( ! class_exists( 'TMDS_Products_Table' ) ) {
				require_once TMDS_INCLUDES . "class" . DIRECTORY_SEPARATOR . 'tmds-products-table.php';
			}
			TMDS_Products_Table::maybe_create_table();
			TMDS_Error_Images_Table::create_table();
		}
		public function check_environment($recent_activate = false ) {
            if ($this->has_pro()){
                return;
            }
			if ( ! class_exists( 'VillaTheme_Require_Environment' ) ) {
				require_once TMDS_INCLUDES . 'support.php';
			}
			$environment = new \VillaTheme_Require_Environment( [
				'plugin_name'     => TMDS_NAME,
				'php_version'     => '7.0',
				'wp_version'       => '6.2',
				'require_plugins' => [
					[
						'slug' => 'woocommerce',
						'name' => 'WooCommerce',
						'defined_version' => 'WC_VERSION',
						'version' => '7.0',
					],
				],
			] );
			if ( $environment->has_error() ) {
				return;
			}
			if ( get_option( 'tmds_setup_wizard' ) &&
			     ( ! empty( $_GET['page'] ) && strpos( sanitize_text_field( wp_unslash( $_GET['page'] ) ), "tmds" ) === 0 )  ) {// phpcs:ignore WordPress.Security.NonceVerification.Recommended
				$url = add_query_arg( [
					'tmds_setup_wizard' => true,
					'_wpnonce'                      => wp_create_nonce( 'tmds_setup' )
				], admin_url() );
				wp_safe_redirect( $url );
				exit();
			}
			global $wpdb;
			$tables = array(
				'tmds_posts'    => 'tmds_posts',
				'tmds_postmeta' => 'tmds_postmeta'
			);
			foreach ( $tables as $name => $table ) {
				$wpdb->$name    = $wpdb->prefix . $table;
				$wpdb->tables[] = $table;
			}
			add_action( 'admin_notices', array( $this, 'admin_notices' ) );
			$this->includes();
			add_action( 'init', array( $this, 'init' ) );
			add_filter( 'plugin_action_links_' . TMDS_BASENAME, array( $this, 'settings_link' ) );
		}
		public function admin_notices() {
			$errors              = [];
			$permalink_structure = get_option( 'permalink_structure' );
			if ( ! $permalink_structure ) {
				$errors[] = sprintf( "%s <a href='%s' target='_blank'>%s</a> %s",
					esc_html__( 'You are using Permalink structure as Plain. Please go to', 'tmds-dropshipping-for-temu-and-woo' ),
					esc_html( admin_url( 'options-permalink.php' ) ),
					esc_html__( 'Permalink Settings', 'tmds-dropshipping-for-temu-and-woo'),
					esc_html__( 'to change it', 'tmds-dropshipping-for-temu-and-woo')
				);
			}

			if ( ! is_ssl() ) {
				$errors[] = sprintf( "%s <a href='https://wordpress.org/documentation/article/https-for-wordpress/' target='_blank'>%s</a>",
					esc_html__( 'Your site is not using HTTPS. For more details, please read', 'tmds-dropshipping-for-temu-and-woo'),
					esc_html__( 'HTTPS for WordPress', 'tmds-dropshipping-for-temu-and-woo' )
				);
			}
			if ( ! empty( $errors ) ) {
				?>
				<div class="error">
					<h3>
						<?php
						echo esc_html( TMDS_NAME ) . ': ' . esc_html( _n( 'you can not import products unless below issue is resolved',
								'you can not import products unless below issues are resolved',
								count( $errors ), 'tmds-dropshipping-for-temu-and-woo' ) );
						?>
					</h3>
					<?php
					foreach ( $errors as $error ) {
						echo wp_kses_post( "<p>{$error}</p>" );
					}
					?>
				</div>
				<?php
			}
		}
		protected function includes() {
			$files = array(
				TMDS_INCLUDES=>[
					'file_name' => [
						'support.php',
						'data.php',
						'background-process/wp-async-request.php',
						'background-process/wp-background-process.php',
						'class/error-images-table.php',
						'class/download-images.php',
						'class/tmds-post.php',
						'class/tmds-post-query.php',
						'class/tmds-products-table.php',
					]
				],
				TMDS_ADMIN=>[
					'class_prefix' => TMDS_Admin_Class_Prefix,
					'file_name' => [
						'api.php',
						'auth.php',
						'log.php',
						'error-images.php',
						'import-list.php',
						'imported.php',
						'product.php',
						'settings.php',
						'setup-wizard.php',
						'recommend.php',
					]
				],
			);
			foreach ( $files as $path => $items ) {
				if (empty($items['file_name']) || !is_array($items['file_name'])){
					continue;
				}
				$class_prefix = $items['class_prefix']??'';
				foreach ($items['file_name'] as $file_name){
					$file = $path.'/'.$file_name;
					if ( !file_exists( $file ) ) {
						continue;
					}
					require_once $file;
					$ext_file  = pathinfo( $file);
					$class_name = $ext_file['filename'] ??'';
					if ($class_prefix){
						$class_name = preg_replace( '/\W/i', '_', $class_prefix . ucfirst( $class_name ) );
					}
					if ( $class_name && class_exists( $class_name ) ) {
						new $class_name;
					}
				}
			}
		}
		public function init() {
			$this->load_plugin_textdomain();
			if ( class_exists( 'VillaTheme_Support' ) ) {
				new VillaTheme_Support(
					array(
						'support'    => 'https://wordpress.org/support/plugin/tmds-dropshipping-for-temu-and-woo/',
						'docs'       => 'https://docs.villatheme.com/?item=tmds',
						'review'     => 'https://wordpress.org/support/plugin/tmds-dropshipping-for-temu-and-woo/reviews/?rate=5#rate-response',
						'pro_url'    => '',
						'css'        => TMDS_CSS,
						'image'      => TMDS_IMAGES,
						'slug'       => 'tmds-dropshipping-for-temu-and-woo',
						'menu_slug'  => 'tmds',
						'version'    => TMDS_VERSION,
						'survey_url' => 'https://script.google.com/macros/s/AKfycbxv5LZHmr8HrfCjdjhkvqirEX4fJVZ__w1kTHWRZBTjUX9EU1eCSZgqJLQWODRwGC70/exec',
					)
				);
			}
		}
		public function load_plugin_textdomain() {
			/**
			 * load Language translate
			 */
			$locale = apply_filters( 'plugin_locale', get_locale(), 'tmds-dropshipping-for-temu-and-woo' );
			load_textdomain( 'tmds-dropshipping-for-temu-and-woo', TMDS_LANGUAGES . "dropshipping-for-temu-and-woo-$locale.mo" );
		}
		public function settings_link( $links ) {
			$settings_link = sprintf( '<a href="%s" title="%s">%s</a>', esc_attr( admin_url( 'admin.php?page=tmds' ) ),
				esc_attr__( 'Settings', 'tmds-dropshipping-for-temu-and-woo' ),
				esc_html__( 'Settings', 'tmds-dropshipping-for-temu-and-woo'  )
			);
			array_unshift( $links, $settings_link );
			return $links;
		}
		public function before_woocommerce_init() {
			if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
				\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', __FILE__, true );
			}
		}
	}
	new VITMDS_Init();
}