=== TMDS - Dropshipping for TEMU and Woo ===
Contributors: villatheme, mrt3vn
Donate link: http://www.villatheme.com/donate
Tags: alidropship, dropship, alidropship woo, temu dropshipping plugin, temu dropshipping
Requires at least: 6.2
Tested up to: 6.8
WC tested up to: 10.0
WC requires at least: 7.0
Requires PHP: 7.0
Stable tag: trunk
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Transfer data from Temu products to WooCommerce effortlessly.

== Description ==

TMDS - Dropshipping for TEMU and Woo allows shop owners to seamlessly import products from Temu into their WooCommerce store. This feature enables store owners to easily build and manage their own e-commerce business, offering a diverse range of products from Temu, and increasing their potential to earn more revenue from online sales.

>[Try the Demo](http://new2new.com/?item=tmds "Demo TMDS") | [Documents](https://docs.villatheme.com/?item=tmds "Documents") | [Download extension](https://downloads.villatheme.com/?download=tmds-extension) | [Facebook group](https://www.facebook.com/groups/villatheme "VillaTheme")

###How to install and use the plugin
[youtube https://youtu.be/HpFB5Rli8CY]


### Important Notice:

- Plugin works based on WooCommerce plugin.

- Your site need use SSL certificates to connect with chrome extension

- Your permalink structure must NOT be "Plain"

- We make no guarantees about the raw information imported from Temu provided by our plugin and will not be held liable if it is not accurate, complete, or up to date, or if it does not otherwise satisfy your specific needs. All risks regarding to raw information and the material may not be excepted and will not be our responsibility.

- There may be instances when information provided through the plugin contains typographical mistakes, inaccuracies, or omissions come from Temu such as product descriptions, promotions, offers, pricing, shipping fee, availability, and delivery timeframes. Apart from official laws require, we make no commitment to correct, update, alter, or clarify such material.
- It is released on WordPress.org and you can use plugin as free to build themes for sale.

### FEATURES

&#9658; **Import Products**:

This plugin allows you to import products from Temu to MULTIPLE WooCommerce stores at a time.

- **Import from single product page**

- **Import from category page**

&#9658; **Configure rules for all imported products**:

You can set general rules for all imported products in the product setting.

- **Global product attributes**: You can choose to import attributes as global or custom attributes

- **Products Status**: Choose a product status for all imported products from Temu. Product status can be: Publish, Pending or Draft.

- **Catalog visibility**: This setting determines which shop pages products will be listed on.

- **Product description**: Customizing product description after adding products to the import list.

- **Select product images**: Enable it, the first image will be selected as the product image and other images (except images from the product description) are selected in the gallery when adding the product to the import list.

- **Images from description**: Image from product description can be downloaded to your server so that you can use other plugins to edit them to suit your store

- **Default categories**: Categories which will be selected by default for products in Import list

- **Default product tags**: Fill in the tag titles.

- **Product variations are visible on the product page**: Enable to make variations of imported products visible on the product page

- **Manage stock**: Enable to manage stock and import product inventory. if you disable this option, the product stock status will be set "Instock" and product inventory will not be imported.

&#9658; **Set up rules for the price of all products**:

- **The Product Price setting**: Allows you to set the price rules for all imported products.

- **Import products currency exchange rate**: Allows you to set the exchange rate to convert the imported product-currency into an expected respective currency in your store.

- **Price from**: In this field, you will set the price rules for multi quantity ranges. You can set a quantity range respectively with an action type.

- **Action**: An action is set respectively with a quantity range. They can be: Increase by a fixed amount; Increase by percentage (%); Set to (a specific value)

- **Sale price**: Set a rule for sale price. Note: Set -1 if there is no sale price

- **Regular price**: Set a rule for regular price.

&#9658; **Configure simple product attributes**:

The products now are in the Import list where you can config product sku, price, edit attributes, select product images, variations... before pushing to WooCommerce store

- **Import all**: Enable to import all products entirely on your Woo store.

- **Search product**: Search for a product on the import list.

- **Product**: Display the imported product attributes.

- **Product Attributes**: You can edit product attributes name, values, remove unwanted attributes before importing

- **Description**: Copy product description on Temu or you can edit it.

- **Variations**: This setting allows you to customize and manage the variable products.

- **Gallery**: Copy gallery on Temu or you can select one on your Woo store.

- **View product on Temu**: Allow your to access Temu directly.

&#9658; **Manage the imported product list**:

You can manage briefly the product attributes in the Imported setting.

- **Product Title**: Name of the product in your store.

- **Sku**: Stock keeping unit of the product in your store

- **Cost**: Price of the product on Temu

- **WooCommerce Price**: Price of the product on your store

- **Delete**: Delete the product on the imported list

&#9658; **Variations Swatches**: Compatible with Product Variations Swatches for WooCommerce. This makes product variations of your store beautiful and easy for customers to select just like Temu

### MAY BE YOU NEED

[9MAIL - WordPress Email Templates Designer](https://wordpress.org/plugins/9mail-wp-email-templates-designer/)

[9Map - Map Multi Locations](https://wordpress.org/plugins/9map-map-multi-locations/)

[Abandoned Cart Recovery for WooCommerce](https://wordpress.org/plugins/woo-abandoned-cart-recovery/)

[Advanced Product Information for WooCommerce](https://wordpress.org/plugins/woo-advanced-product-information/)

[AFFI - Affiliate Marketing for WooCommerce](https://wordpress.org/plugins/affi-affiliate-marketing-for-woo/)

[ALD - Dropshipping and Fulfillment for AliExpress and WooCommerce](https://wordpress.org/plugins/woo-alidropship/)

[Boost Sales for WooCommerce - Set up Up-Sells & Cross-Sells Popups & Auto Apply Coupon](https://wordpress.org/plugins/woo-boost-sales/)

[Bopo - WooCommerce Product Bundle Builder](https://wordpress.org/plugins/bopo-woo-product-bundle-builder/)

[Bulky - Bulk Edit Products for WooCommerce](https://wordpress.org/plugins/bulky-bulk-edit-products-for-woo/)

[Cart All In One For WooCommerce](https://wordpress.org/plugins/woo-cart-all-in-one/)

[Catna - Woo Name Your Price and Offers](https://wordpress.org/plugins/catna-woo-name-your-price-and-offers/)

[Checkout Upsell Funnel for WooCommerce](https://wordpress.org/plugins/checkout-upsell-funnel-for-woo/)

[ChinaDS – Tmall-Taobao Dropshipping for WooCommerce](https://wordpress.org/plugins/chinads-dropshipping-taobao-woocommerce/)

[Clear Autoptimize Cache Automatically](https://wordpress.org/plugins/clear-autoptimize-cache-automatically/)

[COMPE - WooCommerce Compare Products](https://wordpress.org/plugins/compe-woo-compare-products/)

[Coreem - Coupon Reminder for WooCommerce](https://wordpress.org/plugins/woo-coupon-reminder/)

[Coupon Box for WooCommerce](https://wordpress.org/plugins/woo-coupon-box/)

[CURCY - Multi Currency for WooCommerce - The best free currency exchange plugin - Run smoothly on WooCommerce 9.x](https://wordpress.org/plugins/woo-multi-currency/)

[Customer Coupons for WooCommerce](https://wordpress.org/plugins/woo-customer-coupons/)

[DEPART - Deposit and Part payment for Woo](https://wordpress.org/plugins/depart-deposit-and-part-payment-for-woo/)

[Email Template Customizer for WooCommerce](https://wordpress.org/plugins/email-template-customizer-for-woo/)

[EPOI - WP Points and Rewards](https://wordpress.org/plugins/epoi-wp-points-and-rewards/)

[EPOW - Custom Product Options for WooCommerce](https://wordpress.org/plugins/epow-custom-product-options-for-woocommerce/)

[EU Cookies Bar for WordPress](https://wordpress.org/plugins/eu-cookies-bar/)

[EXMAGE - WordPress Image Links](https://wordpress.org/plugins/exmage-wp-image-links/)

[Faview - Virtual Reviews for WooCommerce](https://wordpress.org/plugins/woo-virtual-reviews/)

[FEWC - Extra Checkout Fields For WooCommerce](https://wordpress.org/plugins/fewc-extra-checkout-fields-for-woocommerce/)

[Free Shipping Bar for WooCommerce](https://wordpress.org/plugins/woo-free-shipping-bar/)

[GIFT4U - Gift Cards All in One for Woo](https://wordpress.org/plugins/gift4u-gift-cards-all-in-one-for-woo/)

[HANDMADE - Dropshipping for Etsy and WooCommerce](https://wordpress.org/plugins/handmade-dropshipping-for-etsy-and-woo/)

[HAPPY - Helpdesk Support Ticket System](https://wordpress.org/plugins/happy-helpdesk-support-ticket-system/)

[Jagif - WooCommerce Free Gift](https://wordpress.org/plugins/jagif-woo-free-gift/)

[LookBook for WooCommerce - Shoppable with Product Tags](https://wordpress.org/plugins/woo-lookbook/)

[Lucky Wheel for WooCommerce - Spin a Sale](https://wordpress.org/plugins/woo-lucky-wheel/)

[Notification for WooCommerce | Boost Your Sales - Recent Sales Popup - Live Feed Sales - Upsells](https://wordpress.org/plugins/woo-notification/)

[Orders Tracking for WooCommerce](https://wordpress.org/plugins/woo-orders-tracking/)

[Photo Reviews for WooCommerce](https://wordpress.org/plugins/woo-photo-reviews/)

[Pofily - WooCommerce Product Filters](https://wordpress.org/plugins/pofily-woo-product-filters/)

[Product Builder for WooCommerce - Custom PC Builder](https://wordpress.org/plugins/woo-product-builder/)

[Product Pre-Orders for WooCommerce](https://wordpress.org/plugins/product-pre-orders-for-woo/)

[Product Size Chart For WooCommerce](https://wordpress.org/plugins/product-size-chart-for-woo/)

[Product Variations Swatches for WooCommerce](https://wordpress.org/plugins/product-variations-swatches-for-woocommerce/)

[REDIS - WooCommerce Dynamic Pricing and Discounts](https://wordpress.org/plugins/redis-woo-dynamic-pricing-and-discounts/)

[REES - Real Estate for Woo](https://wordpress.org/plugins/rees-real-estate-for-woo/)

[S2W - Import Shopify to WooCommerce](https://wordpress.org/plugins/import-shopify-to-woocommerce/)

[Sales Countdown Timer](https://wordpress.org/plugins/sales-countdown-timer/)

[SUBRE – Product Subscription for WooCommerce - Recurring Payments](https://wordpress.org/plugins/subre-product-subscription-for-woo/)

[Suggestion Engine for WooCommerce](https://wordpress.org/plugins/woo-suggestion-engine/)

[Thank You Page Customizer for WooCommerce - Increase Your Sales](https://wordpress.org/plugins/woo-thank-you-page-customizer/)

[VARGAL - Additional Variation Gallery for Woo](https://wordpress.org/plugins/vargal-additional-variation-gallery-for-woo/)

[W2S - Migrate WooCommerce to Shopify](https://wordpress.org/plugins/w2s-migrate-woo-to-shopify/)

[WebPOS – Point of Sale for WooCommerce](https://wordpress.org/plugins/webpos-point-of-sale-for-woocommerce/)

[WordPress Lucky Wheel - Spin a Sale](https://wordpress.org/plugins/wp-lucky-wheel/)

[WPBulky - WordPress Bulk Edit Post Types](https://wordpress.org/plugins/wpbulky-wp-bulk-edit-post-types/)

### 3rd party libraries & service
- This plugin relies on Select2 to build the settings function to work properly.
&#9658; [Select2](https://github.com/select2/select2/blob/master/LICENSE.md)
- This plugin includes YouTube videos for storing and providing instructions on how to use its features.
&#9658; [Youtube guide](https://www.youtube.com/watch?v=HpFB5Rli8CY), [Youtube policy](https://www.youtube.com/howyoutubeworks/policies/overview/)
- This plugin displays the Temu images to select before import to WooCommerce stores:
&#9658; [Temu - Terms of use](https://www.temu.com/terms-of-use.html), [Temu - Privacy policy](https://www.temu.com/privacy-and-cookie-policy.html)
- We also suggest products that can be integrated with this plugin to enhance the WooCommerce site design by including images & links to some of our plugins from the following lists:
&#9658; [VillaTheme's suggested plugin](https://profiles.wordpress.org/villatheme/#content-plugins), [WP policy](https://codex.wordpress.org/WordPress_Policies), [VillaTheme policy](https://villatheme.com/privacy-policy/)

### Documentation

- [Getting Started](https://docs.villatheme.com/?item=tmds)

### Plugin Links

- [Project Page](https://villatheme.com/extensions/tmds/)
- [Documentation](https://docs.villatheme.com/?item=tmds)
- [Report Bugs/Issues](https://villatheme.com/knowledge-base/security-is-our-priority/)

== Installation ==

1. Unzip the download package
1. Upload `tmds-dropshipping-for-temu-and-woo` to the `/wp-content/plugins/` directory
1. Activate the plugin through the 'Plugins' menu in WordPress

== Frequently Asked Questions ==

== Screenshots ==
1. Import products on Temu category page using Chrome extension
2. Import products on Temu single product page using Chrome extension
3. Import list page
4. Edit variation before import

== Changelog ==
/**1.0.4 - 2025.07.24**/
- Updated: VillaTheme support
- Updated: Compatible with VARGAL pro - allow setting the variation gallery on the import list

/**1.0.3 - 2025.04.23**/
- Updated: VillaTheme support

/**1.0.2 - 2025.03.07**/
- Updated: VillaTheme support

/**1.0.1 - 2025.02.28**/
- Updated: VillaTheme support

/**1.0.0 - 2025**/
~ The first released
== Upgrade Notice ==