!function(X,Y,G,J){"use strict";Y=void 0!==Y&&Y.Math==Math?Y:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),X.fn.dropdown=function(z){var P,H=X(this),j=X(G),N=H.selector||"",U="ontouchstart"in G.documentElement,K=(new Date).getTime(),W=[],B=z,$="string"==typeof B,Q=[].slice.call(arguments,1);return H.each(function(n){var e,t,i,a,o,s,r,m=X.isPlainObject(z)?X.extend(!0,{},X.fn.dropdown.settings,z):X.extend({},X.fn.dropdown.settings),h=m.className,c=m.message,l=m.fields,g=m.keys,p=m.metadata,u=m.namespace,d=m.regExp,b=m.selector,v=m.error,f=m.templates,w="."+u,x="module-"+u,C=X(this),S=X(m.context),y=C.find(b.text),A=C.find(b.search),T=C.find(b.sizer),k=C.find(b.input),L=C.find(b.icon),I=0<C.prev().find(b.text).length?C.prev().find(b.text):C.prev(),D=C.children(b.menu),q=D.find(b.item),R=!1,O=!1,V=!1,E=this,M=C.data(x),F={initialize:function(){F.debug("Initializing dropdown",m),F.is.alreadySetup()?F.setup.reference():(F.setup.layout(),m.values&&F.change.values(m.values),F.refreshData(),F.save.defaults(),F.restore.selected(),F.create.id(),F.bind.events(),F.observeChanges(),F.instantiate())},instantiate:function(){F.verbose("Storing instance of dropdown",F),M=F,C.data(x,F)},destroy:function(){F.verbose("Destroying previous dropdown",C),F.remove.tabbable(),C.off(w).removeData(x),D.off(w),j.off(a),F.disconnect.menuObserver(),F.disconnect.selectObserver()},observeChanges:function(){"MutationObserver"in Y&&(s=new MutationObserver(F.event.select.mutation),r=new MutationObserver(F.event.menu.mutation),F.debug("Setting up mutation observer",s,r),F.observe.select(),F.observe.menu())},disconnect:{menuObserver:function(){r&&r.disconnect()},selectObserver:function(){s&&s.disconnect()}},observe:{select:function(){F.has.input()&&s.observe(C[0],{childList:!0,subtree:!0})},menu:function(){F.has.menu()&&r.observe(D[0],{childList:!0,subtree:!0})}},create:{id:function(){o=(Math.random().toString(16)+"000000000").substr(2,8),a="."+o,F.verbose("Creating unique id for element",o)},userChoice:function(e){var n,i,a;return!!(e=e||F.get.userValues())&&(e=X.isArray(e)?e:[e],X.each(e,function(e,t){!1===F.get.item(t)&&(a=m.templates.addition(F.add.variables(c.addResult,t)),i=X("<div />").html(a).attr("data-"+p.value,t).attr("data-"+p.text,t).addClass(h.addition).addClass(h.item),m.hideAdditions&&i.addClass(h.hidden),n=n===J?i:n.add(i),F.verbose("Creating user choices for value",t,i))}),n)},userLabels:function(e){var t=F.get.userValues();t&&(F.debug("Adding user labels",t),X.each(t,function(e,t){F.verbose("Adding custom user value"),F.add.label(t,t)}))},menu:function(){D=X("<div />").addClass(h.menu).appendTo(C)},sizer:function(){T=X("<span />").addClass(h.sizer).insertAfter(A)}},search:function(e){e=e!==J?e:F.get.query(),F.verbose("Searching for query",e),F.has.minCharacters(e)?F.filter(e):F.hide()},select:{firstUnfiltered:function(){F.verbose("Selecting first non-filtered element"),F.remove.selectedItem(),q.not(b.unselectable).not(b.addition+b.hidden).eq(0).addClass(h.selected)},nextAvailable:function(e){var t=(e=e.eq(0)).nextAll(b.item).not(b.unselectable).eq(0),n=e.prevAll(b.item).not(b.unselectable).eq(0);0<t.length?(F.verbose("Moving selection to",t),t.addClass(h.selected)):(F.verbose("Moving selection to",n),n.addClass(h.selected))}},setup:{api:function(){var e={debug:m.debug,urlData:{value:F.get.value(),query:F.get.query()},on:!1};F.verbose("First request, initializing API"),C.api(e)},layout:function(){C.is("select")&&(F.setup.select(),F.setup.returnedObject()),F.has.menu()||F.create.menu(),F.is.search()&&!F.has.search()&&(F.verbose("Adding search input"),A=X("<input />").addClass(h.search).prop("autocomplete","off").insertBefore(y)),F.is.multiple()&&F.is.searchSelection()&&!F.has.sizer()&&F.create.sizer(),m.allowTab&&F.set.tabbable()},select:function(){var e=F.get.selectValues();F.debug("Dropdown initialized on a select",e),C.is("select")&&(k=C),0<k.parent(b.dropdown).length?(F.debug("UI dropdown already exists. Creating dropdown menu only"),C=k.closest(b.dropdown),F.has.menu()||F.create.menu(),D=C.children(b.menu),F.setup.menu(e)):(F.debug("Creating entire dropdown from select"),C=X("<div />").attr("class",k.attr("class")).addClass(h.selection).addClass(h.dropdown).html(f.dropdown(e)).insertBefore(k),k.hasClass(h.multiple)&&!1===k.prop("multiple")&&(F.error(v.missingMultiple),k.prop("multiple",!0)),k.is("[multiple]")&&F.set.multiple(),k.prop("disabled")&&(F.debug("Disabling dropdown"),C.addClass(h.disabled)),k.removeAttr("class").detach().prependTo(C)),F.refresh()},menu:function(e){D.html(f.menu(e,l)),q=D.find(b.item)},reference:function(){F.debug("Dropdown behavior was called on select, replacing with closest dropdown"),C=C.parent(b.dropdown),M=C.data(x),E=C.get(0),F.refresh(),F.setup.returnedObject()},returnedObject:function(){var e=H.slice(0,n),t=H.slice(n+1);H=e.add(C).add(t)}},refresh:function(){F.refreshSelectors(),F.refreshData()},refreshItems:function(){q=D.find(b.item)},refreshSelectors:function(){F.verbose("Refreshing selector cache"),y=C.find(b.text),A=C.find(b.search),k=C.find(b.input),L=C.find(b.icon),I=0<C.prev().find(b.text).length?C.prev().find(b.text):C.prev(),D=C.children(b.menu),q=D.find(b.item)},refreshData:function(){F.verbose("Refreshing cached metadata"),q.removeData(p.text).removeData(p.value)},clearData:function(){F.verbose("Clearing metadata"),q.removeData(p.text).removeData(p.value),C.removeData(p.defaultText).removeData(p.defaultValue).removeData(p.placeholderText)},toggle:function(){F.verbose("Toggling menu visibility"),F.is.active()?F.hide():F.show()},show:function(e){if(e=X.isFunction(e)?e:function(){},!F.can.show()&&F.is.remote()&&(F.debug("No API results retrieved, searching before show"),F.queryRemote(F.get.query(),F.show)),F.can.show()&&!F.is.active()){if(F.debug("Showing dropdown"),!F.has.message()||F.has.maxSelections()||F.has.allResultsFiltered()||F.remove.message(),F.is.allFiltered())return!0;!1!==m.onShow.call(E)&&F.animate.show(function(){F.can.click()&&F.bind.intent(),F.has.menuSearch()&&F.focusSearch(),F.set.visible(),e.call(E)})}},hide:function(e){e=X.isFunction(e)?e:function(){},F.is.active()&&!F.is.animatingOutward()&&(F.debug("Hiding dropdown"),!1!==m.onHide.call(E)&&F.animate.hide(function(){F.remove.visible(),e.call(E)}))},hideOthers:function(){F.verbose("Finding other dropdowns to hide"),H.not(C).has(b.menu+"."+h.visible).dropdown("hide")},hideMenu:function(){F.verbose("Hiding menu  instantaneously"),F.remove.active(),F.remove.visible(),D.transition("hide")},hideSubMenus:function(){var e=D.children(b.item).find(b.menu);F.verbose("Hiding sub menus",e),e.transition("hide")},bind:{events:function(){U&&F.bind.touchEvents(),F.bind.keyboardEvents(),F.bind.inputEvents(),F.bind.mouseEvents()},touchEvents:function(){F.debug("Touch device detected binding additional touch events"),F.is.searchSelection()||F.is.single()&&C.on("touchstart"+w,F.event.test.toggle),D.on("touchstart"+w,b.item,F.event.item.mouseenter)},keyboardEvents:function(){F.verbose("Binding keyboard events"),C.on("keydown"+w,F.event.keydown),F.has.search()&&C.on(F.get.inputEvent()+w,b.search,F.event.input),F.is.multiple()&&j.on("keydown"+a,F.event.document.keydown)},inputEvents:function(){F.verbose("Binding input change events"),C.on("change"+w,b.input,F.event.change)},mouseEvents:function(){F.verbose("Binding mouse events"),F.is.multiple()&&C.on("click"+w,b.label,F.event.label.click).on("click"+w,b.remove,F.event.remove.click),F.is.searchSelection()?(C.on("mousedown"+w,F.event.mousedown).on("mouseup"+w,F.event.mouseup).on("mousedown"+w,b.menu,F.event.menu.mousedown).on("mouseup"+w,b.menu,F.event.menu.mouseup).on("click"+w,b.icon,F.event.icon.click).on("focus"+w,b.search,F.event.search.focus).on("click"+w,b.search,F.event.search.focus).on("blur"+w,b.search,F.event.search.blur).on("click"+w,b.text,F.event.text.focus),F.is.multiple()&&C.on("click"+w,F.event.click)):("click"==m.on?C.on("click"+w,F.event.test.toggle):"hover"==m.on?C.on("mouseenter"+w,F.delay.show).on("mouseleave"+w,F.delay.hide):C.on(m.on+w,F.toggle),C.on("click"+w,b.icon,F.event.icon.click).on("mousedown"+w,F.event.mousedown).on("mouseup"+w,F.event.mouseup).on("focus"+w,F.event.focus),F.has.menuSearch()?C.on("blur"+w,b.search,F.event.search.blur):C.on("blur"+w,F.event.blur)),D.on("mouseenter"+w,b.item,F.event.item.mouseenter).on("mouseleave"+w,b.item,F.event.item.mouseleave).on("click"+w,b.item,F.event.item.click)},intent:function(){F.verbose("Binding hide intent event to document"),U&&j.on("touchstart"+a,F.event.test.touch).on("touchmove"+a,F.event.test.touch),j.on("click"+a,F.event.test.hide)}},unbind:{intent:function(){F.verbose("Removing hide intent event from document"),U&&j.off("touchstart"+a).off("touchmove"+a),j.off("click"+a)}},filter:function(e){function t(){F.is.multiple()&&F.filterActive(),(e||!e&&0==F.get.activeItem().length)&&F.select.firstUnfiltered(),F.has.allResultsFiltered()?m.onNoResults.call(E,n)?m.allowAdditions?m.hideAdditions&&(F.verbose("User addition with no menu, setting empty style"),F.set.empty(),F.hideMenu()):(F.verbose("All items filtered, showing message",n),F.add.message(c.noResults)):(F.verbose("All items filtered, hiding dropdown",n),F.hideMenu()):(F.remove.empty(),F.remove.message()),m.allowAdditions&&F.add.userSuggestion(e),F.is.searchSelection()&&F.can.show()&&F.is.focusedOnSearch()&&F.show()}var n=e!==J?e:F.get.query();m.useLabels&&F.has.maxSelections()||(m.apiSettings?F.can.useAPI()?F.queryRemote(n,function(){m.filterRemoteData&&F.filterItems(n),t()}):F.error(v.noAPI):(F.filterItems(n),t()))},queryRemote:function(e,n){var t={errorDuration:!1,cache:"local",throttle:m.throttle,urlData:{query:e},onError:function(){F.add.message(c.serverError),n()},onFailure:function(){F.add.message(c.serverError),n()},onSuccess:function(e){var t=e[l.remoteValues];X.isArray(t)&&0<t.length?(F.remove.message(),F.setup.menu({values:e[l.remoteValues]})):F.add.message(c.noResults),n()}};C.api("get request")||F.setup.api(),t=X.extend(!0,{},t,m.apiSettings),C.api("setting",t).api("query")},filterItems:function(e){var i=e!==J?e:F.get.query(),a=null,t=F.escape.string(i),o=new RegExp("^"+t,"igm");F.has.query()&&(a=[],F.verbose("Searching for matching values",i),q.each(function(){var e,t,n=X(this);if("both"==m.match||"text"==m.match){if(-1!==(e=String(F.get.choiceText(n,!1))).search(o))return a.push(this),!0;if("exact"===m.fullTextSearch&&F.exactSearch(i,e))return a.push(this),!0;if(!0===m.fullTextSearch&&F.fuzzySearch(i,e))return a.push(this),!0}if("both"==m.match||"value"==m.match){if(-1!==(t=String(F.get.choiceValue(n,e))).search(o))return a.push(this),!0;if("exact"===m.fullTextSearch&&F.exactSearch(i,t))return a.push(this),!0;if(!0===m.fullTextSearch&&F.fuzzySearch(i,t))return a.push(this),!0}})),F.debug("Showing only matched items",i),F.remove.filteredItem(),a&&q.not(a).addClass(h.filtered)},fuzzySearch:function(e,t){var n=t.length,i=e.length;if(e=e.toLowerCase(),t=t.toLowerCase(),n<i)return!1;if(i===n)return e===t;e:for(var a=0,o=0;a<i;a++){for(var s=e.charCodeAt(a);o<n;)if(t.charCodeAt(o++)===s)continue e;return!1}return!0},exactSearch:function(e,t){return e=e.toLowerCase(),-1<(t=t.toLowerCase()).indexOf(e)},filterActive:function(){m.useLabels&&q.filter("."+h.active).addClass(h.filtered)},focusSearch:function(e){F.has.search()&&!F.is.focusedOnSearch()&&(e?(C.off("focus"+w,b.search),A.focus(),C.on("focus"+w,b.search,F.event.search.focus)):A.focus())},forceSelection:function(){var e=q.not(h.filtered).filter("."+h.selected).eq(0),t=q.not(h.filtered).filter("."+h.active).eq(0),n=0<e.length?e:t;if(0<n.length&&!F.is.multiple())return F.debug("Forcing partial selection to selected item",n),void F.event.item.click.call(n,{},!0);m.allowAdditions&&F.set.selected(F.get.query()),F.remove.searchTerm()},change:{values:function(e){m.allowAdditions||F.clear(),F.debug("Creating dropdown with specified values",e),F.setup.menu({values:e}),X.each(e,function(e,t){if(1==t.selected)return F.debug("Setting initial selection to",t.value),F.set.selected(t.value),!0})}},event:{change:function(){V||(F.debug("Input changed, updating selection"),F.set.selected())},focus:function(){m.showOnFocus&&!R&&F.is.hidden()&&!t&&F.show()},blur:function(e){t=G.activeElement===this,R||t||(F.remove.activeLabel(),F.hide())},mousedown:function(){F.is.searchSelection()?i=!0:R=!0},mouseup:function(){F.is.searchSelection()?i=!1:R=!1},click:function(e){X(e.target).is(C)&&(F.is.focusedOnSearch()?F.show():F.focusSearch())},search:{focus:function(){R=!0,F.is.multiple()&&F.remove.activeLabel(),m.showOnFocus&&F.search()},blur:function(e){t=G.activeElement===this,F.is.searchSelection()&&!i&&(O||t||(m.forceSelection&&F.forceSelection(),F.hide())),i=!1}},icon:{click:function(e){L.hasClass(h.clear)?F.clear():F.can.click()&&F.toggle()}},text:{focus:function(e){R=!0,F.focusSearch()}},input:function(e){(F.is.multiple()||F.is.searchSelection())&&F.set.filtered(),clearTimeout(F.timer),F.timer=setTimeout(F.search,m.delay.search)},label:{click:function(e){var t=X(this),n=C.find(b.label),i=n.filter("."+h.active),a=t.nextAll("."+h.active),o=t.prevAll("."+h.active),s=0<a.length?t.nextUntil(a).add(i).add(t):t.prevUntil(o).add(i).add(t);e.shiftKey?(i.removeClass(h.active),s.addClass(h.active)):e.ctrlKey?t.toggleClass(h.active):(i.removeClass(h.active),t.addClass(h.active)),m.onLabelSelect.apply(this,n.filter("."+h.active))}},remove:{click:function(){var e=X(this).parent();e.hasClass(h.active)?F.remove.activeLabels():F.remove.activeLabels(e)}},test:{toggle:function(e){var t=F.is.multiple()?F.show:F.toggle;F.is.bubbledLabelClick(e)||F.is.bubbledIconClick(e)||F.determine.eventOnElement(e,t)&&e.preventDefault()},touch:function(e){F.determine.eventOnElement(e,function(){"touchstart"==e.type?F.timer=setTimeout(function(){F.hide()},m.delay.touch):"touchmove"==e.type&&clearTimeout(F.timer)}),e.stopPropagation()},hide:function(e){F.determine.eventInModule(e,F.hide)}},select:{mutation:function(e){F.debug("<select> modified, recreating menu");var n=!1;X.each(e,function(e,t){if(X(t.target).is("select")||X(t.addedNodes).is("select"))return n=!0}),n&&(F.disconnect.selectObserver(),F.refresh(),F.setup.select(),F.set.selected(),F.observe.select())}},menu:{mutation:function(e){var t=e[0],n=t.addedNodes?X(t.addedNodes[0]):X(!1),i=t.removedNodes?X(t.removedNodes[0]):X(!1),a=n.add(i),o=a.is(b.addition)||0<a.closest(b.addition).length,s=a.is(b.message)||0<a.closest(b.message).length;o||s?(F.debug("Updating item selector cache"),F.refreshItems()):(F.debug("Menu modified, updating selector cache"),F.refresh())},mousedown:function(){O=!0},mouseup:function(){O=!1}},item:{mouseenter:function(e){var t=X(e.target),n=X(this),i=n.children(b.menu),a=n.siblings(b.item).children(b.menu),o=0<i.length;0<i.find(t).length||!o||(clearTimeout(F.itemTimer),F.itemTimer=setTimeout(function(){F.verbose("Showing sub-menu",i),X.each(a,function(){F.animate.hide(!1,X(this))}),F.animate.show(!1,i)},m.delay.show),e.preventDefault())},mouseleave:function(e){var t=X(this).children(b.menu);0<t.length&&(clearTimeout(F.itemTimer),F.itemTimer=setTimeout(function(){F.verbose("Hiding sub-menu",t),F.animate.hide(!1,t)},m.delay.hide))},click:function(e,t){var n=X(this),i=X(e?e.target:""),a=n.find(b.menu),o=F.get.choiceText(n),s=F.get.choiceValue(n,o),r=0<a.length,l=0<a.find(i).length;F.has.menuSearch()&&X(G.activeElement).blur(),l||r&&!m.allowCategorySelection||(F.is.searchSelection()&&(m.allowAdditions&&F.remove.userAddition(),F.remove.searchTerm(),F.is.focusedOnSearch()||1==t||F.focusSearch(!0)),m.useLabels||(F.remove.filteredItem(),F.set.scrollPosition(n)),F.determine.selectAction.call(this,o,s))}},document:{keydown:function(e){var t=e.which;if(F.is.inObject(t,g)){var n=C.find(b.label),i=n.filter("."+h.active),a=(i.data(p.value),n.index(i)),o=n.length,s=0<i.length,r=1<i.length,l=0===a,c=a+1==o,u=F.is.searchSelection(),d=F.is.focusedOnSearch(),v=F.is.focused(),f=d&&0===F.get.caretPosition();if(u&&!s&&!d)return;t==g.leftArrow?!v&&!f||s?s&&(e.shiftKey?F.verbose("Adding previous label to selection"):(F.verbose("Selecting previous label"),n.removeClass(h.active)),l&&!r?i.addClass(h.active):i.prev(b.siblingLabel).addClass(h.active).end(),e.preventDefault()):(F.verbose("Selecting previous label"),n.last().addClass(h.active)):t==g.rightArrow?(v&&!s&&n.first().addClass(h.active),s&&(e.shiftKey?F.verbose("Adding next label to selection"):(F.verbose("Selecting next label"),n.removeClass(h.active)),c?u?d?n.removeClass(h.active):F.focusSearch():r?i.next(b.siblingLabel).addClass(h.active):i.addClass(h.active):i.next(b.siblingLabel).addClass(h.active),e.preventDefault())):t==g.deleteKey||t==g.backspace?s?(F.verbose("Removing active labels"),c&&u&&!d&&F.focusSearch(),i.last().next(b.siblingLabel).addClass(h.active),F.remove.activeLabels(i),e.preventDefault()):f&&!s&&t==g.backspace&&(F.verbose("Removing last label on input backspace"),i=n.last().addClass(h.active),F.remove.activeLabels(i)):i.removeClass(h.active)}}},keydown:function(e){var t=e.which;if(F.is.inObject(t,g)){var n,i=q.not(b.unselectable).filter("."+h.selected).eq(0),a=D.children("."+h.active).eq(0),o=0<i.length?i:a,s=0<o.length?o.siblings(":not(."+h.filtered+")").addBack():D.children(":not(."+h.filtered+")"),r=o.children(b.menu),l=o.closest(b.menu),c=l.hasClass(h.visible)||l.hasClass(h.animating)||0<l.parent(b.menu).length,u=0<r.length,d=0<o.length,v=0<o.not(b.unselectable).length,f=t==g.delimiter&&m.allowAdditions&&F.is.multiple();if(m.allowAdditions&&m.hideAdditions&&(t==g.enter||f)&&v&&(F.verbose("Selecting item from keyboard shortcut",o),F.event.item.click.call(o,e),F.is.searchSelection()&&F.remove.searchTerm()),F.is.visible()){if(t!=g.enter&&!f||(t==g.enter&&d&&u&&!m.allowCategorySelection?(F.verbose("Pressed enter on unselectable category, opening sub menu"),t=g.rightArrow):v&&(F.verbose("Selecting item from keyboard shortcut",o),F.event.item.click.call(o,e),F.is.searchSelection()&&F.remove.searchTerm()),e.preventDefault()),d&&(t==g.leftArrow&&l[0]!==D[0]&&(F.verbose("Left key pressed, closing sub-menu"),F.animate.hide(!1,l),o.removeClass(h.selected),l.closest(b.item).addClass(h.selected),e.preventDefault()),t==g.rightArrow&&u&&(F.verbose("Right key pressed, opening sub-menu"),F.animate.show(!1,r),o.removeClass(h.selected),r.find(b.item).eq(0).addClass(h.selected),e.preventDefault())),t==g.upArrow){if(n=d&&c?o.prevAll(b.item+":not("+b.unselectable+")").eq(0):q.eq(0),s.index(n)<0)return F.verbose("Up key pressed but reached top of current menu"),void e.preventDefault();F.verbose("Up key pressed, changing active item"),o.removeClass(h.selected),n.addClass(h.selected),F.set.scrollPosition(n),m.selectOnKeydown&&F.is.single()&&F.set.selectedItem(n),e.preventDefault()}if(t==g.downArrow){if(0===(n=d&&c?n=o.nextAll(b.item+":not("+b.unselectable+")").eq(0):q.eq(0)).length)return F.verbose("Down key pressed but reached bottom of current menu"),void e.preventDefault();F.verbose("Down key pressed, changing active item"),q.removeClass(h.selected),n.addClass(h.selected),F.set.scrollPosition(n),m.selectOnKeydown&&F.is.single()&&F.set.selectedItem(n),e.preventDefault()}t==g.pageUp&&(F.scrollPage("up"),e.preventDefault()),t==g.pageDown&&(F.scrollPage("down"),e.preventDefault()),t==g.escape&&(F.verbose("Escape key pressed, closing dropdown"),F.hide())}else f&&e.preventDefault(),t!=g.downArrow||F.is.visible()||(F.verbose("Down key pressed, showing dropdown"),F.show(),e.preventDefault())}else F.has.search()||F.set.selectedLetter(String.fromCharCode(t))}},trigger:{change:function(){var e=G.createEvent("HTMLEvents"),t=k[0];t&&(F.verbose("Triggering native change event"),e.initEvent("change",!0,!1),t.dispatchEvent(e))}},determine:{selectAction:function(e,t){F.verbose("Determining action",m.action),X.isFunction(F.action[m.action])?(F.verbose("Triggering preset action",m.action,e,t),F.action[m.action].call(E,e,t,this)):X.isFunction(m.action)?(F.verbose("Triggering user action",m.action,e,t),m.action.call(E,e,t,this)):F.error(v.action,m.action)},eventInModule:function(e,t){var n=X(e.target),i=0<n.closest(G.documentElement).length,a=0<n.closest(C).length;return t=X.isFunction(t)?t:function(){},i&&!a?(F.verbose("Triggering event",t),t(),!0):(F.verbose("Event occurred in dropdown, canceling callback"),!1)},eventOnElement:function(e,t){var n=X(e.target),i=n.closest(b.siblingLabel),a=G.body.contains(e.target),o=0===C.find(i).length,s=0===n.closest(D).length;return t=X.isFunction(t)?t:function(){},a&&o&&s?(F.verbose("Triggering event",t),t(),!0):(F.verbose("Event occurred in dropdown menu, canceling callback"),!1)}},action:{nothing:function(){},activate:function(e,t,n){if(t=t!==J?t:e,F.can.activate(X(n))){if(F.set.selected(t,X(n)),F.is.multiple()&&!F.is.allFiltered())return;F.hideAndClear()}},select:function(e,t,n){if(t=t!==J?t:e,F.can.activate(X(n))){if(F.set.value(t,e,X(n)),F.is.multiple()&&!F.is.allFiltered())return;F.hideAndClear()}},combo:function(e,t,n){t=t!==J?t:e,F.set.selected(t,X(n)),F.hideAndClear()},hide:function(e,t,n){F.set.value(t,e,X(n)),F.hideAndClear()}},get:{id:function(){return o},defaultText:function(){return C.data(p.defaultText)},defaultValue:function(){return C.data(p.defaultValue)},placeholderText:function(){return"auto"!=m.placeholder&&"string"==typeof m.placeholder?m.placeholder:C.data(p.placeholderText)||""},text:function(){return y.text()},query:function(){return X.trim(A.val())},searchWidth:function(e){return e=e!==J?e:A.val(),T.text(e),Math.ceil(T.width()+1)},selectionCount:function(){var e=F.get.values();return F.is.multiple()?X.isArray(e)?e.length:0:""!==F.get.value()?1:0},transition:function(e){return"auto"==m.transition?F.is.upward(e)?"slide up":"slide down":m.transition},userValues:function(){var e=F.get.values();return!!e&&(e=X.isArray(e)?e:[e],X.grep(e,function(e){return!1===F.get.item(e)}))},uniqueArray:function(n){return X.grep(n,function(e,t){return X.inArray(e,n)===t})},caretPosition:function(){var e,t,n=A.get(0);return"selectionStart"in n?n.selectionStart:G.selection?(n.focus(),t=(e=G.selection.createRange()).text.length,e.moveStart("character",-n.value.length),e.text.length-t):void 0},value:function(){var e=0<k.length?k.val():C.data(p.value),t=X.isArray(e)&&1===e.length&&""===e[0];return e===J||t?"":e},values:function(){var e=F.get.value();return""===e?"":!F.has.selectInput()&&F.is.multiple()?"string"==typeof e?e.split(m.delimiter):"":e},remoteValues:function(){var e=F.get.values(),i=!1;return e&&("string"==typeof e&&(e=[e]),X.each(e,function(e,t){var n=F.read.remoteData(t);F.verbose("Restoring value from session data",n,t),n&&((i=i||{})[t]=n)})),i},choiceText:function(e,t){if(t=t!==J?t:m.preserveHTML,e)return 0<e.find(b.menu).length&&(F.verbose("Retrieving text of element with sub-menu"),(e=e.clone()).find(b.menu).remove(),e.find(b.menuIcon).remove()),e.data(p.text)!==J?e.data(p.text):t?X.trim(e.html()):X.trim(e.text())},choiceValue:function(e,t){return t=t||F.get.choiceText(e),!!e&&(e.data(p.value)!==J?String(e.data(p.value)):"string"==typeof t?X.trim(t.toLowerCase()):String(t))},inputEvent:function(){var e=A[0];return!!e&&(e.oninput!==J?"input":e.onpropertychange!==J?"propertychange":"keyup")},selectValues:function(){var a={values:[]};return C.find("option").each(function(){var e=X(this),t=e.html(),n=e.attr("disabled"),i=e.attr("value")!==J?e.attr("value"):t;"auto"===m.placeholder&&""===i?a.placeholder=t:a.values.push({name:t,value:i,disabled:n})}),m.placeholder&&"auto"!==m.placeholder&&(F.debug("Setting placeholder value to",m.placeholder),a.placeholder=m.placeholder),m.sortSelect?(a.values.sort(function(e,t){return e.name>t.name?1:-1}),F.debug("Retrieved and sorted values from select",a)):F.debug("Retrieved values from select",a),a},activeItem:function(){return q.filter("."+h.active)},selectedItem:function(){var e=q.not(b.unselectable).filter("."+h.selected);return 0<e.length?e:q.eq(0)},itemWithAdditions:function(e){var t=F.get.item(e),n=F.create.userChoice(e);return n&&0<n.length&&(t=0<t.length?t.add(n):n),t},item:function(i,a){var e,o,s=!1;return i=i!==J?i:F.get.values()!==J?F.get.values():F.get.text(),e=o?0<i.length:i!==J&&null!==i,o=F.is.multiple()&&X.isArray(i),a=""===i||0===i||(a||!1),e&&q.each(function(){var e=X(this),t=F.get.choiceText(e),n=F.get.choiceValue(e,t);if(null!==n&&n!==J)if(o)-1===X.inArray(String(n),i)&&-1===X.inArray(t,i)||(s=s?s.add(e):e);else if(a){if(F.verbose("Ambiguous dropdown value using strict type check",e,i),n===i||t===i)return s=e,!0}else if(String(n)==String(i)||t==i)return F.verbose("Found select item by value",n,i),s=e,!0}),s}},check:{maxSelections:function(e){return!m.maxSelections||((e=e!==J?e:F.get.selectionCount())>=m.maxSelections?(F.debug("Maximum selection count reached"),m.useLabels&&(q.addClass(h.filtered),F.add.message(c.maxSelections)),!0):(F.verbose("No longer at maximum selection count"),F.remove.message(),F.remove.filteredItem(),F.is.searchSelection()&&F.filterItems(),!1))}},restore:{defaults:function(){F.clear(),F.restore.defaultText(),F.restore.defaultValue()},defaultText:function(){var e=F.get.defaultText();e===F.get.placeholderText?(F.debug("Restoring default placeholder text",e),F.set.placeholderText(e)):(F.debug("Restoring default text",e),F.set.text(e))},placeholderText:function(){F.set.placeholderText()},defaultValue:function(){var e=F.get.defaultValue();e!==J&&(F.debug("Restoring default value",e),""!==e?(F.set.value(e),F.set.selected()):(F.remove.activeItem(),F.remove.selectedItem()))},labels:function(){m.allowAdditions&&(m.useLabels||(F.error(v.labels),m.useLabels=!0),F.debug("Restoring selected values"),F.create.userLabels()),F.check.maxSelections()},selected:function(){F.restore.values(),F.is.multiple()?(F.debug("Restoring previously selected values and labels"),F.restore.labels()):F.debug("Restoring previously selected values")},values:function(){F.set.initialLoad(),m.apiSettings&&m.saveRemoteData&&F.get.remoteValues()?F.restore.remoteValues():F.set.selected(),F.remove.initialLoad()},remoteValues:function(){var e=F.get.remoteValues();F.debug("Recreating selected from session data",e),e&&(F.is.single()?X.each(e,function(e,t){F.set.text(t)}):X.each(e,function(e,t){F.add.label(e,t)}))}},read:{remoteData:function(e){var t;if(Y.Storage!==J)return(t=sessionStorage.getItem(e))!==J&&t;F.error(v.noStorage)}},save:{defaults:function(){F.save.defaultText(),F.save.placeholderText(),F.save.defaultValue()},defaultValue:function(){var e=F.get.value();F.verbose("Saving default value as",e),C.data(p.defaultValue,e)},defaultText:function(){var e=F.get.text();F.verbose("Saving default text as",e),C.data(p.defaultText,e)},placeholderText:function(){var e;!1!==m.placeholder&&y.hasClass(h.placeholder)&&(e=F.get.text(),F.verbose("Saving placeholder text as",e),C.data(p.placeholderText,e))},remoteData:function(e,t){Y.Storage!==J?(F.verbose("Saving remote data to session storage",t,e),sessionStorage.setItem(t,e)):F.error(v.noStorage)}},clear:function(){F.is.multiple()&&m.useLabels?F.remove.labels():(F.remove.activeItem(),F.remove.selectedItem()),F.set.placeholderText(),F.clearValue()},clearValue:function(){F.set.value("")},scrollPage:function(e,t){var n=t||F.get.selectedItem(),i=n.closest(b.menu),a=i.outerHeight(),o=i.scrollTop(),s=q.eq(0).outerHeight(),r=Math.floor(a/s),l=(i.prop("scrollHeight"),"up"==e?o-s*r:o+s*r),c=q.not(b.unselectable),u="up"==e?c.index(n)-r:c.index(n)+r,d=("up"==e?0<=u:u<c.length)?c.eq(u):"up"==e?c.first():c.last();0<d.length&&(F.debug("Scrolling page",e,d),n.removeClass(h.selected),d.addClass(h.selected),m.selectOnKeydown&&F.is.single()&&F.set.selectedItem(d),i.scrollTop(l))},set:{filtered:function(){var e=F.is.multiple(),t=F.is.searchSelection(),n=e&&t,i=t?F.get.query():"",a="string"==typeof i&&0<i.length,o=F.get.searchWidth(),s=""!==i;e&&a&&(F.verbose("Adjusting input width",o,m.glyphWidth),A.css("width",o)),a||n&&s?(F.verbose("Hiding placeholder text"),y.addClass(h.filtered)):e&&(!n||s)||(F.verbose("Showing placeholder text"),y.removeClass(h.filtered))},empty:function(){C.addClass(h.empty)},loading:function(){C.addClass(h.loading)},placeholderText:function(e){e=e||F.get.placeholderText(),F.debug("Setting placeholder text",e),F.set.text(e),y.addClass(h.placeholder)},tabbable:function(){F.is.searchSelection()?(F.debug("Added tabindex to searchable dropdown"),A.val("").attr("tabindex",0),D.attr("tabindex",-1)):(F.debug("Added tabindex to dropdown"),C.attr("tabindex")===J&&(C.attr("tabindex",0),D.attr("tabindex",-1)))},initialLoad:function(){F.verbose("Setting initial load"),e=!0},activeItem:function(e){m.allowAdditions&&0<e.filter(b.addition).length?e.addClass(h.filtered):e.addClass(h.active)},partialSearch:function(e){var t=F.get.query().length;A.val(e.substr(0,t))},scrollPosition:function(e,t){var n,i,a,o,s=(e=e||F.get.selectedItem()).closest(b.menu),r=e&&0<e.length;t=t!==J&&t,e&&0<s.length&&r&&(e.position().top,s.addClass(h.loading),n=(i=s.scrollTop())-s.offset().top+e.offset().top,t||(o=i+s.height()<n+5,a=n-5<i),F.debug("Scrolling to active item",n),(t||a||o)&&s.scrollTop(n),s.removeClass(h.loading))},text:function(e){"select"!==m.action&&("combo"==m.action?(F.debug("Changing combo button text",e,I),m.preserveHTML?I.html(e):I.text(e)):(e!==F.get.placeholderText()&&y.removeClass(h.placeholder),F.debug("Changing text",e,y),y.removeClass(h.filtered),m.preserveHTML?y.html(e):y.text(e)))},selectedItem:function(e){var t=F.get.choiceValue(e),n=F.get.choiceText(e,!1),i=F.get.choiceText(e,!0);F.debug("Setting user selection to item",e),F.remove.activeItem(),F.set.partialSearch(n),F.set.activeItem(e),F.set.selected(t,e),F.set.text(i)},selectedLetter:function(e){var t,n=q.filter("."+h.selected),i=0<n.length&&F.has.firstLetter(n,e),a=!1;i&&(t=n.nextAll(q).eq(0),F.has.firstLetter(t,e)&&(a=t)),a||q.each(function(){if(F.has.firstLetter(X(this),e))return a=X(this),!1}),a&&(F.verbose("Scrolling to next value with letter",e),F.set.scrollPosition(a),n.removeClass(h.selected),a.addClass(h.selected),m.selectOnKeydown&&F.is.single()&&F.set.selectedItem(a))},direction:function(e){"auto"==m.direction?(F.remove.upward(),F.can.openDownward(e)?F.remove.upward(e):F.set.upward(e),F.is.leftward(e)||F.can.openRightward(e)||F.set.leftward(e)):"upward"==m.direction&&F.set.upward(e)},upward:function(e){(e||C).addClass(h.upward)},leftward:function(e){(e||D).addClass(h.leftward)},value:function(e,t,n){var i=F.escape.value(e),a=0<k.length,o=F.get.values(),s=e!==J?String(e):e;if(a){if(!m.allowReselection&&s==o&&(F.verbose("Skipping value update already same value",e,o),!F.is.initialLoad()))return;F.is.single()&&F.has.selectInput()&&F.can.extendSelect()&&(F.debug("Adding user option",e),F.add.optionValue(e)),F.debug("Updating input value",i,o),V=!0,k.val(i),!1===m.fireOnInit&&F.is.initialLoad()?F.debug("Input native change event ignored on initial load"):F.trigger.change(),V=!1}else F.verbose("Storing value in metadata",i,k),i!==o&&C.data(p.value,s);F.is.single()&&m.clearable&&(i?F.set.clearable():F.remove.clearable()),!1===m.fireOnInit&&F.is.initialLoad()?F.verbose("No callback on initial load",m.onChange):m.onChange.call(E,e,t,n)},active:function(){C.addClass(h.active)},multiple:function(){C.addClass(h.multiple)},visible:function(){C.addClass(h.visible)},exactly:function(e,t){F.debug("Setting selected to exact values"),F.clear(),F.set.selected(e,t)},selected:function(e,r){var l=F.is.multiple();(r=m.allowAdditions?r||F.get.itemWithAdditions(e):r||F.get.item(e))&&(F.debug("Setting selected menu item to",r),F.is.multiple()&&F.remove.searchWidth(),F.is.single()?(F.remove.activeItem(),F.remove.selectedItem()):m.useLabels&&F.remove.selectedItem(),r.each(function(){var e=X(this),t=F.get.choiceText(e),n=F.get.choiceValue(e,t),i=e.hasClass(h.filtered),a=e.hasClass(h.active),o=e.hasClass(h.addition),s=l&&1==r.length;l?!a||o?(m.apiSettings&&m.saveRemoteData&&F.save.remoteData(t,n),m.useLabels?(F.add.label(n,t,s),F.add.value(n,t,e),F.set.activeItem(e),F.filterActive(),F.select.nextAvailable(r)):(F.add.value(n,t,e),F.set.text(F.add.variables(c.count)),F.set.activeItem(e))):i||(F.debug("Selected active value, removing label"),F.remove.selected(n)):(m.apiSettings&&m.saveRemoteData&&F.save.remoteData(t,n),F.set.text(t),F.set.value(n,t,e),e.addClass(h.active).addClass(h.selected))}))},clearable:function(){L.addClass(h.clear)}},add:{label:function(e,t,n){var i,a=F.is.searchSelection()?A:y,o=F.escape.value(e);m.ignoreCase&&(o=o.toLowerCase()),i=X("<a />").addClass(h.label).attr("data-"+p.value,o).html(f.label(o,t)),i=m.onLabelCreate.call(i,o,t),F.has.label(e)?F.debug("User selection already exists, skipping",o):(m.label.variation&&i.addClass(m.label.variation),!0===n?(F.debug("Animating in label",i),i.addClass(h.hidden).insertBefore(a).transition(m.label.transition,m.label.duration)):(F.debug("Adding selection label",i),i.insertBefore(a)))},message:function(e){var t=D.children(b.message),n=m.templates.message(F.add.variables(e));0<t.length?t.html(n):t=X("<div/>").html(n).addClass(h.message).appendTo(D)},optionValue:function(e){var t=F.escape.value(e);0<k.find('option[value="'+F.escape.string(t)+'"]').length||(F.disconnect.selectObserver(),F.is.single()&&(F.verbose("Removing previous user addition"),k.find("option."+h.addition).remove()),X("<option/>").prop("value",t).addClass(h.addition).html(e).appendTo(k),F.verbose("Adding user addition as an <option>",e),F.observe.select())},userSuggestion:function(e){var t,n=D.children(b.addition),i=F.get.item(e),a=i&&i.not(b.addition).length,o=0<n.length;m.useLabels&&F.has.maxSelections()||(""===e||a?n.remove():(o?(n.data(p.value,e).data(p.text,e).attr("data-"+p.value,e).attr("data-"+p.text,e).removeClass(h.filtered),m.hideAdditions||(t=m.templates.addition(F.add.variables(c.addResult,e)),n.html(t)),F.verbose("Replacing user suggestion with new value",n)):((n=F.create.userChoice(e)).prependTo(D),F.verbose("Adding item choice to menu corresponding with user choice addition",n)),m.hideAdditions&&!F.is.allFiltered()||n.addClass(h.selected).siblings().removeClass(h.selected),F.refreshItems()))},variables:function(e,t){var n,i,a=-1!==e.search("{count}"),o=-1!==e.search("{maxCount}"),s=-1!==e.search("{term}");return F.verbose("Adding templated variables to message",e),a&&(n=F.get.selectionCount(),e=e.replace("{count}",n)),o&&(n=F.get.selectionCount(),e=e.replace("{maxCount}",m.maxSelections)),s&&(i=t||F.get.query(),e=e.replace("{term}",i)),e},value:function(e,t,n){var i,a=F.get.values();F.has.value(e)?F.debug("Value already selected"):""!==e?(i=X.isArray(a)?(i=a.concat([e]),F.get.uniqueArray(i)):[e],F.has.selectInput()?F.can.extendSelect()&&(F.debug("Adding value to select",e,i,k),F.add.optionValue(e)):(i=i.join(m.delimiter),F.debug("Setting hidden input to delimited value",i,k)),!1===m.fireOnInit&&F.is.initialLoad()?F.verbose("Skipping onadd callback on initial load",m.onAdd):m.onAdd.call(E,e,t,n),F.set.value(i,e,t,n),F.check.maxSelections()):F.debug("Cannot select blank values from multiselect")}},remove:{active:function(){C.removeClass(h.active)},activeLabel:function(){C.find(b.label).removeClass(h.active)},empty:function(){C.removeClass(h.empty)},loading:function(){C.removeClass(h.loading)},initialLoad:function(){e=!1},upward:function(e){(e||C).removeClass(h.upward)},leftward:function(e){(e||D).removeClass(h.leftward)},visible:function(){C.removeClass(h.visible)},activeItem:function(){q.removeClass(h.active)},filteredItem:function(){m.useLabels&&F.has.maxSelections()||(m.useLabels&&F.is.multiple()?q.not("."+h.active).removeClass(h.filtered):q.removeClass(h.filtered),F.remove.empty())},optionValue:function(e){var t=F.escape.value(e),n=k.find('option[value="'+F.escape.string(t)+'"]');0<n.length&&n.hasClass(h.addition)&&(s&&(s.disconnect(),F.verbose("Temporarily disconnecting mutation observer")),n.remove(),F.verbose("Removing user addition as an <option>",t),s&&s.observe(k[0],{childList:!0,subtree:!0}))},message:function(){D.children(b.message).remove()},searchWidth:function(){A.css("width","")},searchTerm:function(){F.verbose("Cleared search term"),A.val(""),F.set.filtered()},userAddition:function(){q.filter(b.addition).remove()},selected:function(e,t){if(!(t=m.allowAdditions?t||F.get.itemWithAdditions(e):t||F.get.item(e)))return!1;t.each(function(){var e=X(this),t=F.get.choiceText(e),n=F.get.choiceValue(e,t);F.is.multiple()?m.useLabels?(F.remove.value(n,t,e),F.remove.label(n)):(F.remove.value(n,t,e),0===F.get.selectionCount()?F.set.placeholderText():F.set.text(F.add.variables(c.count))):F.remove.value(n,t,e),e.removeClass(h.filtered).removeClass(h.active),m.useLabels&&e.removeClass(h.selected)})},selectedItem:function(){q.removeClass(h.selected)},value:function(e,t,n){var i,a=F.get.values();F.has.selectInput()?(F.verbose("Input is <select> removing selected option",e),i=F.remove.arrayValue(e,a),F.remove.optionValue(e)):(F.verbose("Removing from delimited values",e),i=(i=F.remove.arrayValue(e,a)).join(m.delimiter)),!1===m.fireOnInit&&F.is.initialLoad()?F.verbose("No callback on initial load",m.onRemove):m.onRemove.call(E,e,t,n),F.set.value(i,t,n),F.check.maxSelections()},arrayValue:function(t,e){return X.isArray(e)||(e=[e]),e=X.grep(e,function(e){return t!=e}),F.verbose("Removed value from delimited string",t,e),e},label:function(e,t){var n=C.find(b.label).filter("[data-"+p.value+'="'+F.escape.string(e)+'"]');F.verbose("Removing label",n),n.remove()},activeLabels:function(e){e=e||C.find(b.label).filter("."+h.active),F.verbose("Removing active label selections",e),F.remove.labels(e)},labels:function(e){e=e||C.find(b.label),F.verbose("Removing labels",e),e.each(function(){var e=X(this),t=e.data(p.value),n=t!==J?String(t):t,i=F.is.userValue(n);!1!==m.onLabelRemove.call(e,t)?(F.remove.message(),i?(F.remove.value(n),F.remove.label(n)):F.remove.selected(n)):F.debug("Label remove callback cancelled removal")})},tabbable:function(){F.is.searchSelection()?(F.debug("Searchable dropdown initialized"),A.removeAttr("tabindex")):(F.debug("Simple selection dropdown initialized"),C.removeAttr("tabindex")),D.removeAttr("tabindex")},clearable:function(){L.removeClass(h.clear)}},has:{menuSearch:function(){return F.has.search()&&0<A.closest(D).length},search:function(){return 0<A.length},sizer:function(){return 0<T.length},selectInput:function(){return k.is("select")},minCharacters:function(e){return!m.minCharacters||(e=e!==J?String(e):String(F.get.query())).length>=m.minCharacters},firstLetter:function(e,t){var n;return!(!e||0===e.length||"string"!=typeof t)&&(n=F.get.choiceText(e,!1),(t=t.toLowerCase())==String(n).charAt(0).toLowerCase())},input:function(){return 0<k.length},items:function(){return 0<q.length},menu:function(){return 0<D.length},message:function(){return 0!==D.children(b.message).length},label:function(e){var t=F.escape.value(e),n=C.find(b.label);return m.ignoreCase&&(t=t.toLowerCase()),0<n.filter("[data-"+p.value+'="'+F.escape.string(t)+'"]').length},maxSelections:function(){return m.maxSelections&&F.get.selectionCount()>=m.maxSelections},allResultsFiltered:function(){var e=q.not(b.addition);return e.filter(b.unselectable).length===e.length},userSuggestion:function(){return 0<D.children(b.addition).length},query:function(){return""!==F.get.query()},value:function(e){return m.ignoreCase?F.has.valueIgnoringCase(e):F.has.valueMatchingCase(e)},valueMatchingCase:function(e){var t=F.get.values();return!!(X.isArray(t)?t&&-1!==X.inArray(e,t):t==e)},valueIgnoringCase:function(n){var e=F.get.values(),i=!1;return X.isArray(e)||(e=[e]),X.each(e,function(e,t){if(String(n).toLowerCase()==String(t).toLowerCase())return!(i=!0)}),i}},is:{active:function(){return C.hasClass(h.active)},animatingInward:function(){return D.transition("is inward")},animatingOutward:function(){return D.transition("is outward")},bubbledLabelClick:function(e){return X(e.target).is("select, input")&&0<C.closest("label").length},bubbledIconClick:function(e){return 0<X(e.target).closest(L).length},alreadySetup:function(){return C.is("select")&&C.parent(b.dropdown).data(x)!==J&&0===C.prev().length},animating:function(e){return e?e.transition&&e.transition("is animating"):D.transition&&D.transition("is animating")},leftward:function(e){return(e||D).hasClass(h.leftward)},disabled:function(){return C.hasClass(h.disabled)},focused:function(){return G.activeElement===C[0]},focusedOnSearch:function(){return G.activeElement===A[0]},allFiltered:function(){return(F.is.multiple()||F.has.search())&&!(0==m.hideAdditions&&F.has.userSuggestion())&&!F.has.message()&&F.has.allResultsFiltered()},hidden:function(e){return!F.is.visible(e)},initialLoad:function(){return e},inObject:function(n,e){var i=!1;return X.each(e,function(e,t){if(t==n)return i=!0}),i},multiple:function(){return C.hasClass(h.multiple)},remote:function(){return m.apiSettings&&F.can.useAPI()},single:function(){return!F.is.multiple()},selectMutation:function(e){var n=!1;return X.each(e,function(e,t){if(t.target&&X(t.target).is("select"))return n=!0}),n},search:function(){return C.hasClass(h.search)},searchSelection:function(){return F.has.search()&&1===A.parent(b.dropdown).length},selection:function(){return C.hasClass(h.selection)},userValue:function(e){return-1!==X.inArray(e,F.get.userValues())},upward:function(e){return(e||C).hasClass(h.upward)},visible:function(e){return e?e.hasClass(h.visible):D.hasClass(h.visible)},verticallyScrollableContext:function(){var e=S.get(0)!==Y&&S.css("overflow-y");return"auto"==e||"scroll"==e},horizontallyScrollableContext:function(){var e=S.get(0)!==Y&&S.css("overflow-X");return"auto"==e||"scroll"==e}},can:{activate:function(e){return!!m.useLabels||(!F.has.maxSelections()||!(!F.has.maxSelections()||!e.hasClass(h.active)))},openDownward:function(e){var t,n,i=e||D,a=!0;return i.addClass(h.loading),n={context:{offset:S.get(0)===Y?{top:0,left:0}:S.offset(),scrollTop:S.scrollTop(),height:S.outerHeight()},menu:{offset:i.offset(),height:i.outerHeight()}},F.is.verticallyScrollableContext()&&(n.menu.offset.top+=n.context.scrollTop),a=(t={above:n.context.scrollTop<=n.menu.offset.top-n.context.offset.top-n.menu.height,below:n.context.scrollTop+n.context.height>=n.menu.offset.top-n.context.offset.top+n.menu.height}).below?(F.verbose("Dropdown can fit in context downward",t),!0):t.below||t.above?(F.verbose("Dropdown cannot fit below, opening upward",t),!1):(F.verbose("Dropdown cannot fit in either direction, favoring downward",t),!0),i.removeClass(h.loading),a},openRightward:function(e){var t,n,i=e||D,a=!0;return i.addClass(h.loading),n={context:{offset:S.get(0)===Y?{top:0,left:0}:S.offset(),scrollLeft:S.scrollLeft(),width:S.outerWidth()},menu:{offset:i.offset(),width:i.outerWidth()}},F.is.horizontallyScrollableContext()&&(n.menu.offset.left+=n.context.scrollLeft),(t=n.menu.offset.left-n.context.offset.left+n.menu.width>=n.context.scrollLeft+n.context.width)&&(F.verbose("Dropdown cannot fit in context rightward",t),a=!1),i.removeClass(h.loading),a},click:function(){return U||"click"==m.on},extendSelect:function(){return m.allowAdditions||m.apiSettings},show:function(){return!F.is.disabled()&&(F.has.items()||F.has.message())},useAPI:function(){return X.fn.api!==J}},animate:{show:function(e,t){var n,i=t||D,a=t?function(){}:function(){F.hideSubMenus(),F.hideOthers(),F.set.active()};e=X.isFunction(e)?e:function(){},F.verbose("Doing menu show animation",i),F.set.direction(t),n=F.get.transition(t),F.is.selection()&&F.set.scrollPosition(F.get.selectedItem(),!0),(F.is.hidden(i)||F.is.animating(i))&&("none"==n?(a(),i.transition("show"),e.call(E)):X.fn.transition!==J&&C.transition("is supported")?i.transition({animation:n+" in",debug:m.debug,verbose:m.verbose,duration:m.duration,queue:!0,onStart:a,onComplete:function(){e.call(E)}}):F.error(v.noTransition,n))},hide:function(e,t){var n=t||D,i=(t?m.duration:m.duration,t?function(){}:function(){F.can.click()&&F.unbind.intent(),F.remove.active()}),a=F.get.transition(t);e=X.isFunction(e)?e:function(){},(F.is.visible(n)||F.is.animating(n))&&(F.verbose("Doing menu hide animation",n),"none"==a?(i(),n.transition("hide"),e.call(E)):X.fn.transition!==J&&C.transition("is supported")?n.transition({animation:a+" out",duration:m.duration,debug:m.debug,verbose:m.verbose,queue:!1,onStart:i,onComplete:function(){e.call(E)}}):F.error(v.transition))}},hideAndClear:function(){F.remove.searchTerm(),F.has.maxSelections()||(F.has.search()?F.hide(function(){F.remove.filteredItem()}):F.hide())},delay:{show:function(){F.verbose("Delaying show event to ensure user intent"),clearTimeout(F.timer),F.timer=setTimeout(F.show,m.delay.show)},hide:function(){F.verbose("Delaying hide event to ensure user intent"),clearTimeout(F.timer),F.timer=setTimeout(F.hide,m.delay.hide)}},escape:{value:function(e){var t=X.isArray(e),n="string"==typeof e,i=!n&&!t,a=n&&-1!==e.search(d.quote),o=[];return i||!a?e:(F.debug("Encoding quote values for use in select",e),t?(X.each(e,function(e,t){o.push(t.replace(d.quote,"&quot;"))}),o):e.replace(d.quote,"&quot;"))},string:function(e){return(e=String(e)).replace(d.escape,"\\$&")}},setting:function(e,t){if(F.debug("Changing setting",e,t),X.isPlainObject(e))X.extend(!0,m,e);else{if(t===J)return m[e];X.isPlainObject(m[e])?X.extend(!0,m[e],t):m[e]=t}},internal:function(e,t){if(X.isPlainObject(e))X.extend(!0,F,e);else{if(t===J)return F[e];F[e]=t}},debug:function(){!m.silent&&m.debug&&(m.performance?F.performance.log(arguments):(F.debug=Function.prototype.bind.call(console.info,console,m.name+":"),F.debug.apply(console,arguments)))},verbose:function(){!m.silent&&m.verbose&&m.debug&&(m.performance?F.performance.log(arguments):(F.verbose=Function.prototype.bind.call(console.info,console,m.name+":"),F.verbose.apply(console,arguments)))},error:function(){m.silent||(F.error=Function.prototype.bind.call(console.error,console,m.name+":"),F.error.apply(console,arguments))},performance:{log:function(e){var t,n;m.performance&&(n=(t=(new Date).getTime())-(K||t),K=t,W.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:E,"Execution Time":n})),clearTimeout(F.performance.timer),F.performance.timer=setTimeout(F.performance.display,500)},display:function(){var e=m.name+":",n=0;K=!1,clearTimeout(F.performance.timer),X.each(W,function(e,t){n+=t["Execution Time"]}),e+=" "+n+"ms",N&&(e+=" '"+N+"'"),(console.group!==J||console.table!==J)&&0<W.length&&(console.groupCollapsed(e),console.table?console.table(W):X.each(W,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),W=[]}},invoke:function(i,e,t){var a,o,n,s=M;return e=e||Q,t=E||t,"string"==typeof i&&s!==J&&(i=i.split(/[\. ]/),a=i.length-1,X.each(i,function(e,t){var n=e!=a?t+i[e+1].charAt(0).toUpperCase()+i[e+1].slice(1):i;if(X.isPlainObject(s[n])&&e!=a)s=s[n];else{if(s[n]!==J)return o=s[n],!1;if(!X.isPlainObject(s[t])||e==a)return s[t]!==J?o=s[t]:F.error(v.method,i),!1;s=s[t]}})),X.isFunction(o)?n=o.apply(t,e):o!==J&&(n=o),X.isArray(P)?P.push(n):P!==J?P=[P,n]:n!==J&&(P=n),o}};$?(M===J&&F.initialize(),F.invoke(B)):(M!==J&&M.invoke("destroy"),F.initialize())}),P!==J?P:H},X.fn.dropdown.settings={silent:!1,debug:!1,verbose:!1,performance:!0,on:"click",action:"activate",values:!1,clearable:!1,apiSettings:!1,selectOnKeydown:!0,minCharacters:0,filterRemoteData:!1,saveRemoteData:!0,throttle:200,context:Y,direction:"auto",keepOnScreen:!0,match:"both",fullTextSearch:!1,placeholder:"auto",preserveHTML:!0,sortSelect:!1,forceSelection:!0,allowAdditions:!1,ignoreCase:!1,hideAdditions:!0,maxSelections:!1,useLabels:!0,delimiter:",",showOnFocus:!0,allowReselection:!1,allowTab:!0,allowCategorySelection:!1,fireOnInit:!1,transition:"auto",duration:200,glyphWidth:1.037,label:{transition:"scale",duration:200,variation:!1},delay:{hide:300,show:200,search:20,touch:50},onChange:function(e,t,n){},onAdd:function(e,t,n){},onRemove:function(e,t,n){},onLabelSelect:function(e){},onLabelCreate:function(e,t){return X(this)},onLabelRemove:function(e){return!0},onNoResults:function(e){return!0},onShow:function(){},onHide:function(){},name:"Dropdown",namespace:"dropdown",message:{addResult:"Add <b>{term}</b>",count:"{count} selected",maxSelections:"Max {maxCount} selections",noResults:"No results found.",serverError:"There was an error contacting the server"},error:{action:"You called a dropdown action that was not defined",alreadySetup:"Once a select has been initialized behaviors must be called on the created ui dropdown",labels:"Allowing user additions currently requires the use of labels.",missingMultiple:"<select> requires multiple property to be set to correctly preserve multiple values",method:"The method you called is not defined.",noAPI:"The API module is required to load resources remotely",noStorage:"Saving remote data requires session storage",noTransition:"This module requires ui transitions <https://github.com/Semantic-Org/UI-Transition>"},regExp:{escape:/[-[\]{}()*+?.,\\^$|#\s]/g,quote:/"/g},metadata:{defaultText:"defaultText",defaultValue:"defaultValue",placeholderText:"placeholder",text:"text",value:"value"},fields:{remoteValues:"results",values:"values",disabled:"disabled",name:"name",value:"value",text:"text"},keys:{backspace:8,delimiter:188,deleteKey:46,enter:13,escape:27,pageUp:33,pageDown:34,leftArrow:37,upArrow:38,rightArrow:39,downArrow:40},selector:{addition:".addition",dropdown:".vi-ui.dropdown",hidden:".hidden",icon:"> .dropdown.icon",input:'> input[type="hidden"], > select',item:".item",label:"> .label",remove:"> .label > .delete.icon",siblingLabel:".label",menu:".menu",message:".message",menuIcon:".dropdown.icon",search:"input.search, .menu > .search > input, .menu input.search",sizer:"> input.sizer",text:"> .text:not(.icon)",unselectable:".disabled, .filtered"},className:{active:"active",addition:"addition",animating:"animating",clear:"clear",disabled:"disabled",empty:"empty",dropdown:"ui dropdown",filtered:"filtered",hidden:"hidden transition",item:"item",label:"ui label",loading:"loading",menu:"menu",message:"message",multiple:"multiple",placeholder:"default",sizer:"sizer",search:"search",selected:"selected",selection:"selection",upward:"upward",leftward:"left",visible:"visible"}},X.fn.dropdown.settings.templates={dropdown:function(e){var t=e.placeholder||!1,n=(e.values,"");return n+='<i class="dropdown icon"></i>',e.placeholder?n+='<div class="default text">'+t+"</div>":n+='<div class="text"></div>',n+='<div class="menu">',X.each(e.values,function(e,t){n+=t.disabled?'<div class="disabled item" data-value="'+t.value+'">'+t.name+"</div>":'<div class="item" data-value="'+t.value+'">'+t.name+"</div>"}),n+="</div>"},menu:function(e,a){var t=e[a.values]||{},o="";return X.each(t,function(e,t){var n=t[a.text]?'data-text="'+t[a.text]+'"':"",i=t[a.disabled]?"disabled ":"";o+='<div class="'+i+'item" data-value="'+t[a.value]+'"'+n+">",o+=t[a.name],o+="</div>"}),o},label:function(e,t){return t+'<i class="delete icon"></i>'},message:function(e){return e},addition:function(e){return e}}}(jQuery,window,document);